# Permission System Testing Guide

This document explains how to test the role-based access control system that has been implemented for the admin interface.

## Overview

The permission system includes:
- **Permission Guard Hook** (`usePermissionGuard`): Checks user permissions and handles redirects
- **Route Permission Configuration** (`routePermissions.ts`): Maps routes to required permissions
- **Permission Guard Component**: Wraps admin pages to enforce access control
- **No Access Page**: Fallback page for users without any permissions

## How It Works

1. **Permission Check**: When a user navigates to any admin page, the system checks if their role has the required permissions for that page.

2. **Redirect Logic**: If the user lacks permissions:
   - The system checks sidebar navigation items in order
   - Finds the first item the user can access
   - Automatically redirects to that accessible page

3. **Fallback**: If no accessible pages are found, redirects to `/no-access`

## Testing the System

### 1. Browser Console Testing

Open the browser console and run:

```javascript
// Run all permission tests
window.testPermissions.runAllTests();

// Test specific user scenarios
window.testPermissions.testPermissions();

// Test route configuration
window.testPermissions.testRouteConfig();

// Check if a specific user can access a route
const user = window.testPermissions.mockUsers.dashboardOnlyUser;
const canAccess = window.testPermissions.canAccessPage(user, ['dashboard']);
console.log('Can access dashboard:', canAccess);
```

### 2. Manual Testing Scenarios

#### Scenario 1: Super Admin
- **Expected**: Can access all pages
- **Test**: Navigate to any admin page - should have full access

#### Scenario 2: Limited Permissions User
- **Expected**: Only sees accessible navigation items, gets redirected from unauthorized pages
- **Test**: 
  1. Modify user permissions in Redux store (browser dev tools)
  2. Navigate to unauthorized page
  3. Should redirect to first accessible page

#### Scenario 3: No Permissions User
- **Expected**: Redirected to `/no-access` page
- **Test**: 
  1. Clear all user permissions
  2. Navigate to any admin page
  3. Should see "Access Denied" page

### 3. Testing Different User Types

The system includes mock users for testing:

```javascript
// Available mock users
const users = window.testPermissions.mockUsers;

// Super Admin - has all permissions
users.superAdmin

// Dashboard Only - can only access dashboard
users.dashboardOnlyUser

// Users Manager - can only manage users
users.usersOnlyUser

// No Permissions - has no permissions
users.noPermissionsUser

// Trading Manager - can access trading features
users.tradingUser
```

## Route Permission Configuration

Routes and their required permissions are configured in `src/config/routePermissions.ts`:

```typescript
export const routePermissions = {
  '/': [PermissionEnum.DASHBOARD],
  '/users': [PermissionEnum.USERS],
  '/kyc': [PermissionEnum.KYC],
  '/admins': [PermissionEnum.ADMINS],
  '/roles': [PermissionEnum.ROLES],
  // ... more routes
};
```

## Adding New Protected Routes

1. **Add route to configuration**:
   ```typescript
   // In src/config/routePermissions.ts
   '/new-feature': [PermissionEnum.NEW_FEATURE],
   ```

2. **Add permission enum**:
   ```typescript
   // In src/types/enums.ts
   export enum PermissionEnum {
     // ... existing permissions
     NEW_FEATURE = 'new_feature',
   }
   ```

3. **Add to sidebar navigation**:
   ```typescript
   // In src/layout/AppSidebar.tsx
   {
     name: 'New Feature',
     path: '/new-feature',
     requiredPermissions: [PermissionEnum.NEW_FEATURE],
   }
   ```

## Debugging

### Common Issues

1. **User not redirecting**: Check if user data is loaded (`userInfo.admin_id` exists)
2. **Infinite redirects**: Ensure at least one route is accessible to the user
3. **Permission not working**: Verify route is configured in `routePermissions.ts`

### Debug Tools

```javascript
// Check current user permissions
console.log('User:', window.store.getState().user.userInfo);

// Check route permissions
console.log('Route permissions:', window.testPermissions.getRoutePermissions('/current-route'));

// Check accessible routes for user
const userPermissions = window.store.getState().user.userInfo.permissions.map(p => p.resource);
console.log('Accessible routes:', window.testPermissions.getAccessibleRoutes(userPermissions));
```

## Security Notes

- The permission system is client-side only and should be backed by server-side authorization
- Always validate permissions on the backend for API calls
- The system prevents UI access but doesn't secure API endpoints
- Consider implementing API-level permission checks for complete security

## Files Modified/Created

- `src/hooks/usePermissionGuard.ts` - Permission guard hook
- `src/config/routePermissions.ts` - Route permission configuration
- `src/components/guards/PermissionGuard.tsx` - Permission guard component
- `src/app/(admin)/(others-pages)/no-access/` - No access page
- `src/utils/permissions.ts` - Updated with new permission functions
- `src/app/(admin)/layout.tsx` - Integrated permission guard
- `src/utils/testPermissions.ts` - Testing utilities
