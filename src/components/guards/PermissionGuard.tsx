'use client';

import React from 'react';
import { usePermissionGuard } from '@/hooks/usePermissionGuard';

interface PermissionGuardProps {
  children: React.ReactNode;
}

/**
 * Permission Guard Component
 * Wraps admin pages to check permissions and handle redirects
 */
export default function PermissionGuard({ children }: PermissionGuardProps) {
  const { isLoading, hasAccess, isRedirecting } = usePermissionGuard();

  // Show loading state while checking permissions or redirecting
  if (isLoading || isRedirecting) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {isLoading ? 'Loading...' : 'Redirecting...'}
          </p>
        </div>
      </div>
    );
  }

  // If user doesn't have access and we're not redirecting, show access denied
  if (!hasAccess) {
    return (
      <div className="flex min-h-[60vh] flex-col items-center justify-center text-center">
        <div className="mx-auto max-w-md">
          <div className="mb-8 flex justify-center">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
              <svg
                className="h-10 w-10 text-red-600 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
                />
              </svg>
            </div>
          </div>
          <h1 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white/90">
            Access Denied
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  // User has access, render the protected content
  return <>{children}</>;
}
