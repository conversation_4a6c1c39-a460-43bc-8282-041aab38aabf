'use client';

import React from 'react';
import { usePermissionGuard } from '@/hooks/usePermissionGuard';

interface PermissionGuardProps {
  children: React.ReactNode;
}

/**
 * Permission Guard Component
 * Wraps admin pages to check permissions and handle redirects
 */
export default function PermissionGuard({ children }: PermissionGuardProps) {
  const { isLoading, hasAccess, isRedirecting } = usePermissionGuard();

  // Show loading state while checking permissions or redirecting
  if (isLoading || isRedirecting) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {isLoading ? 'Loading...' : 'Redirecting...'}
          </p>
        </div>
      </div>
    );
  }

  // The hook handles all the redirect logic, so if we get here with hasAccess=true, render content
  // If hasAccess=false, the hook should have redirected us, but just in case show loading
  if (!hasAccess) {
    return (
      <div className="flex min-h-[60vh] items-center justify-center">
        <div className="text-center">
          <div className="mb-4 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // User has access, render the protected content
  return <>{children}</>;
}
