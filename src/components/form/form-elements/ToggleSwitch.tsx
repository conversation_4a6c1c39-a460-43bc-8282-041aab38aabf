'use client';
import React from 'react';
import ComponentCard from '../../common/ComponentCard';
import Switch from '../switch/Switch';

export default function ToggleSwitch() {
  const handleSwitchChange = () => {
    console.log('Switch is now');
  };
  return (
    <ComponentCard title="Toggle switch input">
      <div className="flex gap-4">
        <Switch label="Default" value={true} onChange={handleSwitchChange} />
        <Switch label="Checked" value={true} onChange={handleSwitchChange} />
      </div>{' '}
      <div className="flex gap-4">
        <Switch label="Default" value={true} onChange={handleSwitchChange} color="gray" />
        <Switch label="Checked" value={true} onChange={handleSwitchChange} color="gray" />
      </div>
    </ComponentCard>
  );
}
