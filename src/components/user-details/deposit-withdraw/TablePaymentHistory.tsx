'use client';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import Badge from '@/components/ui/badge/Badge';
import { formatUnixTimestamp } from '@/utils/format';
import { ChevronDownIcon, RefreshIcon } from '@/icons';
import rf from '@/services/RequestFactory';
import BigNumber from 'bignumber.js';
import { TTransactionHistory } from '@/types/transaction';
import Label from '@/components/form/Label';
import Select from '@/components/form/Select';
import { useWindowSize } from '@/hooks/useWindowSize';
import { filterParams } from '@/utils/helper';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useParams } from 'next/navigation';

const OPTIONS_TYPE = [
  { value: 'ALL', label: 'All' },
  { value: 'DEPOSIT', label: 'Receive' },
  { value: 'WITHDRAW', label: 'Paid' },
];

const OPTIONS_STATUS = [
  { value: 'ALL', label: 'All' },
  { value: 'COMPLETED', label: 'Completed' },
  { value: 'PROCESSING', label: 'Processing' },
  { value: 'PENDING', label: 'Pending' },
  { value: 'FAILED', label: 'Failed' },
  { value: 'REJECTED', label: 'Rejected' },
];

const TablePaymentHistory = () => {
  const { userId } = useParams();
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const [type, setType] = useState<string>('ALL');
  const [status, setStatus] = useState<string>('ALL');
  const [coin, setCoin] = useState<string>('');

  const { windowHeight } = useWindowSize();

  const assets = useSelector((state: RootState) => state.metadata.assets);
  const optionsAsset = useMemo(
    () => [
      { label: 'All', value: '' },
      ...assets.map((asset) => ({
        label: asset.symbol,
        value: asset.symbol,
      })),
    ],
    [assets]
  );

  const getTraderHistory = useCallback(
    async (params: any) => {
      try {
        const result = await rf.getRequest('AccountRequest').getTransactionHistories(
          filterParams({
            ...params,
            type: type?.toUpperCase(),
            isExternal: false,
            status,
            coin,
            userIdOrEmail: userId,
          })
        );

        return {
          cursor: result?.cursor,
          data: result?.docs || [],
        };
      } catch (err) {
        console.log(err, 'getTransactionHistories error');
        return { cursor: null, data: [] };
      }
    },
    [type, status, coin]
  );

  const tableHeight = useMemo(() => {
    return windowHeight - 450;
  }, [windowHeight]);

  const onRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <div className="flex items-end gap-x-6">
          <div>
            <Label>Asset</Label>
            <div className="relative">
              <Select
                options={optionsAsset}
                defaultValue={coin}
                onChange={(value: string) => setCoin(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <div>
            <Label>Type</Label>
            <div className="relative">
              <Select
                options={OPTIONS_TYPE}
                defaultValue={type}
                onChange={(value: string) => setType(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <div>
            <Label>Status</Label>
            <div className="relative">
              <Select
                defaultValue={status}
                options={OPTIONS_STATUS}
                onChange={(value: string) => setStatus(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
        </div>

        <div onClick={onRefreshData} className="cursor-pointer text-gray-800 dark:text-white/90">
          <RefreshIcon />
        </div>
      </div>

      <div className="overflow-hidden">
        <AppDataTable
          height={tableHeight}
          minWidth={1200}
          ref={dataTableRef}
          getData={getTraderHistory as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Time
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Type
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Assets
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Amount
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  To/From
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-end font-medium text-gray-500 dark:text-gray-400">
                  Status
                </div>
              </div>
            );
          }}
          renderRow={(tx: TTransactionHistory) => {
            return (
              <div
                key={tx.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[20%] items-center px-4 py-3 text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(tx.timestamp)}
                </div>
                <div className="text-theme-sm flex w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx?.type?.toUpperCase() === 'DEPOSIT' ? 'Receive' : 'Paid'}
                </div>
                <div className="text-theme-sm w-[20%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.asset}
                </div>
                <div className="text-theme-sm w-[20%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.type?.toUpperCase() === 'DEPOSIT' ? '+' : '-'}{' '}
                  {new BigNumber(tx.amount).toFormat()}
                </div>

                <div className="text-theme-sm flex w-[20%] items-center gap-2 px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {tx?.type?.toUpperCase() === 'DEPOSIT' ? tx.fromUserId : tx.toUserId}
                </div>
                <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-end capitalize text-gray-500 dark:text-gray-400">
                  <Badge
                    size="sm"
                    color={
                      tx.status === 'COMPLETED'
                        ? 'success'
                        : tx.status === 'PROCESSING' || tx.status === 'PENDING'
                        ? 'warning'
                        : 'error'
                    }
                  >
                    {tx.status?.toLowerCase()}
                  </Badge>
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TablePaymentHistory;
