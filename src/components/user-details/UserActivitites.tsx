'use client';

import React, { useCallback, useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import rf from '@/services/RequestFactory';
import { useParams } from 'next/navigation';
import { useWindowSize } from '@/hooks/useWindowSize';
import { formatUnixTimestamp } from '@/utils/format';
import Badge from '@/components/ui/badge/Badge';

const UserActivities = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { userId } = useParams();
  const { windowHeight } = useWindowSize();

  const getData = useCallback(
    async (params: any) => {
      if (!userId) return { data: [], cursor: null };
      try {
        const res = await rf.getRequest('UserRequest').getUserActivities({
          ...params,
          user_id: userId,
        });
        return { data: res?.docs, cursor: res?.cursor };
      } catch (e: any) {
        console.error('Get Activities User Error', e?.message);
        return { data: [], cursor: null };
      }
    },
    [userId]
  );

  const tableHeight = useMemo(() => {
    return windowHeight - 350;
  }, [windowHeight]);

  return (
    <>
      <div className="overflow-hidden">
        <AppDataTable
          minWidth={1000}
          ref={dataTableRef}
          getData={getData as any}
          height={tableHeight}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Time
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Category
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Request Type
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  IP Address
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Source
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 font-medium text-gray-500 dark:text-gray-400">
                  Status
                </div>
              </div>
            );
          }}
          renderRow={(activity: any, index: number) => {
            return (
              <div
                key={index}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(activity?.createdAt * 1000)}
                </div>
                <div className="text-theme-sm w-[15%] px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {activity?.activityCategory}
                </div>
                <div className="text-theme-sm w-[15%] px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {activity?.requestType}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {activity?.ipAddress}
                </div>
                <div className="text-theme-sm w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {activity?.source}
                </div>
                <div className="text-theme-sm w-[15%] px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <Badge size="sm" color={activity.status === 'Completed' ? 'success' : 'error'}>
                    {activity.status?.toLowerCase()}
                  </Badge>
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default UserActivities;
