'use client';

import React from 'react';
import Switch from '../form/switch/Switch';
import { toastError, toastSuccess } from '@/libs/toast';
import rf from '@/services/RequestFactory';
import { useParams } from 'next/navigation';

const UserSetting = ({
  onFetchData,
  userDetails,
}: {
  userDetails: any;
  onFetchData: () => void;
}) => {
  const { userId } = useParams();
  const handleChangeConfigWithdraw = async () => {
    if (!userId) return;
    try {
      if (userDetails.withdrawEnabled) {
        await rf.getRequest('AccountRequest').disableUserWithdrawal({
          userId: +userId,
        });
        toastSuccess('Success', 'Disable Successfully!');
      } else {
        await rf.getRequest('AccountRequest').enableUserWithdrawal({
          userId: +userId,
        });
        toastSuccess('Success', 'Enable Successfully!');
      }
      onFetchData();
    } catch (e: any) {
      toastError('Error', e?.message || 'Something went wrong!');
      console.error('Config Withdraw', e?.message);
    }
  };

  // const handleChangeConfigDeposit = async () => {
  //   if (!userId) return;
  //   try {
  //     if (userDetails.depositEnabled) {
  //       await rf.getRequest('AccountRequest').disableUserDeposit({
  //         userId: +userId,
  //       });
  //       toastSuccess('Success', 'Disable Successfully!');
  //     } else {
  //       await rf.getRequest('AccountRequest').enableUserDeposit({
  //         userId: +userId,
  //       });
  //       toastSuccess('Success', 'Enable Successfully!');
  //     }
  //     onFetchData();
  //   } catch (e: any) {
  //     toastError('Error', e?.message || 'Something went wrong!');
  //     console.error('Config Withdraw', e?.message);
  //   }
  // };

  return (
    <div className="flex flex-col gap-4 pt-4 text-sm text-gray-500 dark:border-white/[0.05] dark:text-gray-400">
      {/*<div className="flex items-center justify-between">*/}
      {/*  <div className="text-sm">Deposit Enabled</div>*/}
      {/*  <Switch value={userDetails?.depositEnabled} onChange={handleChangeConfigDeposit} />*/}
      {/*</div>*/}
      <div className="flex items-center justify-between">
        <div className="text-sm">Withdraw Enabled</div>
        <Switch value={userDetails?.withdrawEnabled} onChange={handleChangeConfigWithdraw} />
      </div>
    </div>
  );
};

export default UserSetting;
