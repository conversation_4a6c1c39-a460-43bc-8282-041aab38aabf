'use client';
import Input from '@/components/form/input/InputField';
import Label from '@/components/form/Label';
import { EyeCloseIcon, EyeIcon } from '@/icons';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { toastError, toastSuccess } from '@/libs/toast';
import { useForm } from 'react-hook-form';
import rf from '@/services/RequestFactory';
import { useRouter } from 'next/navigation';

export default function WelcomeForm() {
  const searchParams = useSearchParams();
  const username = searchParams.get('username');
  const email = searchParams.get('email');
  const registrationToken = searchParams.get('registrationToken');

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    defaultValues: {
      username: '',
      email: '',
      confirmPassword: '',
      password: '',
    },
  } as any);

  useEffect(() => {
    if (email && username) {
      reset({
        username: username,
        email: email,
      });
    }
  }, [email, username]);

  const onSubmit = async (data: any) => {
    try {
      await rf.getRequest('AdminRequest').acceptInviteAdmin({
        password: data.password,
        registration_token: registrationToken,
      });
      await router.push('/signin');
      toastSuccess('Success', 'Accept Successfully!');
    } catch (e: any) {
      toastError('Error', e?.message);
      console.log(e?.message, 'Accept Error');
    }
  };

  const password = watch('password');

  return (
    <div className="no-scrollbar flex w-full flex-1 flex-col justify-center overflow-y-auto lg:w-1/2">
      <div className="mx-auto flex w-full max-w-md flex-col items-center justify-center">
        <div className="mb-6 block flex items-center justify-center lg:hidden">
          <Image width={160} height={40} src="./images/logo/logo-icon1.svg" alt="Logo" />
        </div>
        <div>
          <div className="mb-5 sm:mb-8">
            <h1 className="text-title-sm sm:text-title-md mb-2 font-semibold text-gray-800 dark:text-white/90">
              Welcome to VDAX Admin
            </h1>
          </div>
          <div>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-5">
                <div>
                  <Label>
                    User Name<span className="text-error-500">*</span>
                  </Label>
                  <Input
                    disabled
                    {...register('username', { required: true })}
                    type="text"
                    placeholder="Enter your username"
                  />
                </div>

                <div>
                  <Label>
                    Email<span className="text-error-500">*</span>
                  </Label>
                  <Input
                    disabled
                    type="email"
                    {...register('email', { required: true })}
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <Label>
                    Password<span className="text-error-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      {...register('password', { required: true })}
                      placeholder="Enter your password"
                      type={showPassword ? 'text' : 'password'}
                    />
                    <span
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 z-30 -translate-y-1/2 cursor-pointer"
                    >
                      {showPassword ? (
                        <EyeIcon className="fill-gray-500 dark:fill-gray-400" />
                      ) : (
                        <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400" />
                      )}
                    </span>
                  </div>
                  {errors.password && (
                    <p className="text-xs text-red-400">This field is required</p>
                  )}
                </div>

                <div>
                  <Label>
                    Confirm Password<span className="text-error-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      {...register('confirmPassword', {
                        required: 'Please confirm your password',
                        validate: (value) => value === password || 'Passwords do not match',
                      })}
                      placeholder="Enter your confirm password"
                      type={showConfirmPassword ? 'text' : 'password'}
                    />
                    <span
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-4 top-1/2 z-30 -translate-y-1/2 cursor-pointer"
                    >
                      {showConfirmPassword ? (
                        <EyeIcon className="fill-gray-500 dark:fill-gray-400" />
                      ) : (
                        <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400" />
                      )}
                    </span>
                  </div>
                  {typeof errors.confirmPassword?.message === 'string' && (
                    <p className="text-xs text-red-400">{errors.confirmPassword.message}</p>
                  )}
                </div>

                <div>
                  <button
                    type="submit"
                    className="bg-brand-500 shadow-theme-xs hover:bg-brand-600 flex w-full items-center justify-center rounded-lg px-4 py-3 text-sm font-medium text-white transition"
                  >
                    Submit
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
