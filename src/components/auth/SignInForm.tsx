'use client';

import Input from '@/components/form/input/InputField';
import Label from '@/components/form/Label';
import Button from '@/components/ui/button/Button';
import { EyeCloseIcon, EyeIcon } from '@/icons';
import React, { useState } from 'react';
import Image from 'next/image';
import { toastError } from '@/libs/toast';
import rf from '@/services/RequestFactory';
import { toastSuccess } from '@/libs/toast';
import { useDispatch } from 'react-redux';
import { setUserInfo } from '@/store/user.store';
import { useAuth } from '@/app/provider';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

export default function SignInForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const { updateToken } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const onLogin = async (data: any) => {
    try {
      setIsLoading(true);
      const response = await rf.getRequest('AuthRequest').login(data);
      if (!response?.success) {
        toastError('Error', response?.response?.data?.error || 'Login Error');
        setIsLoading(false);
        return;
      }
      // Set basic user info from login response
      dispatch(
        setUserInfo({
          user: {
            name: response?.data?.name,
            email: response?.data?.email,
            role: response?.data?.role,
          },
        })
      );

      toastSuccess('Success', 'Login Successfully!');
      setIsLoading(false);

      try {
        // Try to get the token from the server and update the client-side auth
        const tokenResponse = await fetch('/api/auth/token');
        const tokenData = await tokenResponse.json();

        if (tokenData.token) {
          updateToken(tokenData.token);

          setTimeout(() => {
            window.location.href = '/';
          }, 100);
        } else {
          window.location.href = '/';
        }
      } catch (error) {
        console.log('Token fetch failed, using fallback:', error);
        // Fallback to full page reload
        window.location.href = '/';
      }
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : 'Login Error';
      toastError('Error', errorMessage);
      setIsLoading(false);
      console.error(errorMessage);
    }
  };

  return (
    <div className="flex w-full flex-1 flex-col lg:w-1/2">
      <div className="mx-auto flex w-full max-w-md flex-1 flex-col justify-center ">
        <div className="mb-10 block flex items-center justify-center lg:hidden">
          <Image width={160} height={40} src="./images/logo/logo-icon1.svg" alt="Logo" />
        </div>
        <div>
          <div className="mb-5 sm:mb-8">
            <h1 className="text-title-sm sm:text-title-md mb-2 font-semibold text-gray-800 dark:text-white/90">
              Sign In
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Enter your email and password to sign in!
            </p>
          </div>
          <form onSubmit={handleSubmit(onLogin)}>
            <div className="space-y-6">
              <div>
                <Label>
                  Email <span className="text-error-500">*</span>{' '}
                </Label>
                <Input
                  {...register('email', { required: true })}
                  placeholder="<EMAIL>"
                  type="email"
                />
                {errors.email && <p className="text-xs text-red-400">This field is required</p>}
              </div>
              <div>
                <Label>
                  Password <span className="text-error-500">*</span>{' '}
                </Label>
                <div className="relative">
                  <Input
                    {...register('password', { required: true })}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                  />
                  <span
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 z-30 -translate-y-1/2 cursor-pointer"
                  >
                    {showPassword ? (
                      <EyeIcon className="fill-gray-500 dark:fill-gray-400" />
                    ) : (
                      <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400" />
                    )}
                  </span>
                </div>
                {errors.password && <p className="text-xs text-red-400">This field is required</p>}
              </div>
              <div>
                <Button type="submit" className="w-full" size="sm" disabled={isLoading}>
                  Sign in
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
