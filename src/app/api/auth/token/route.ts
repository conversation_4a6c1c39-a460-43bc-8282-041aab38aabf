import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { COOKIES_ACCESS_TOKEN_KEY } from '@/constants';

export async function GET() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIES_ACCESS_TOKEN_KEY)?.value || '';

    return NextResponse.json({ token });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
