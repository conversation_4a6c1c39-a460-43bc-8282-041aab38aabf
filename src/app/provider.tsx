'use client';

import { Provider } from 'react-redux';
import { ReactNode, useEffect, createContext, useContext } from 'react';
import { store } from '@/store';
import { setAuthorizationToRequest } from '../utils/auth';
import { getNetworks, getAssets } from '@/store/metadata.store';
import { getUserProfile, logoutUser } from '../store/user.store';
import { AppBroadcast, BROADCAST_EVENTS } from '@/libs/broadcast';

const AuthContext = createContext<{
  updateToken: (token: string) => void;
}>({
  updateToken: () => {},
});

// Hook to access the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AppProvider = ({
  children,
  authorization,
}: {
  children: ReactNode;
  authorization?: string;
}) => {
  const initializeMetadata = () => {
    store.dispatch(getNetworks());
    store.dispatch(getAssets());
  };

  const updateToken = (token: string) => {
    setAuthorizationToRequest(token);
    store.dispatch(getUserProfile()).catch((error) => {
      console.log('Profile fetch failed after token update:', error);
    });
  };

  useEffect(() => {
    initializeMetadata();
  }, []);

  useEffect(() => {
    if (authorization) {
      setAuthorizationToRequest(authorization);
      store.dispatch(getUserProfile()).catch((error) => {
        console.log('Initial profile fetch failed:', error);
        // The error handling is already done in the getUserProfile thunk
      });
      return;
    }
  }, [authorization]);

  useEffect(() => {
    // Listen for logout broadcast events
    const handleLogout = () => {
      console.log('Logout broadcast event received');
      store.dispatch(logoutUser());
    };

    AppBroadcast.on(BROADCAST_EVENTS.LOGOUT, handleLogout);

    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.LOGOUT, handleLogout);
    };
  }, []);

  return (
    <AuthContext.Provider value={{ updateToken }}>
      <Provider store={store}>{children}</Provider>
    </AuthContext.Provider>
  );
};
