import { Outfit } from 'next/font/google';
import './globals.css';
import type { Metadata, Viewport } from 'next';
import { SidebarProvider } from '@/context/SidebarContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { AppProvider } from './provider';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
const outfit = Outfit({
  subsets: ['latin'],
});
import { cookies } from 'next/headers';
import { COOKIES_ACCESS_TOKEN_KEY } from '@/constants';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  userScalable: false,
  maximumScale: 1,
  viewportFit: 'cover',
  themeColor: '#000000',
};

export const metadata: Metadata = {
  title: 'VDAX Admin',
  description: 'VDAX Admin',
  icons: {
    icon: '/images/logo/logo-icon.svg',
    apple: '/images/logo/logo-icon.svg',
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const accessToken = (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || '';

  return (
    <html lang="en">
      <body className={`${outfit.className} dark:bg-gray-900`}>
        <AppProvider authorization={accessToken}>
          <ToastContainer
            autoClose={2000}
            position="top-right"
            icon={false}
            pauseOnHover
            closeButton={false}
            hideProgressBar
            toastStyle={{
              position: 'relative',
              overflow: 'visible',
            }}
          />
          <ThemeProvider>
            <SidebarProvider>{children}</SidebarProvider>
          </ThemeProvider>
        </AppProvider>
      </body>
    </html>
  );
}
