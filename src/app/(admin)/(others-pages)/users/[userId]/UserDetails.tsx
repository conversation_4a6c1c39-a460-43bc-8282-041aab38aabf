'use client';

import { useEffect, useState } from 'react';
import UserBalance from '@/components/user-details/UserBalance';
import { UserInfo } from '@/components/user-details/UserInfo';
import TableOpenOrder from '@/components/user-details/trades/TableOpenOrder';
import TableTrades from '@/components/user-details/trades/TableTrades';
import TableOrderHistories from '@/components/user-details/trades/TableOrderHistories';
import TableTransactions from '@/components/user-details/deposit-withdraw/TableTransactions';
import TablePaymentHistory from '@/components/user-details/deposit-withdraw/TablePaymentHistory';
import TabBar from '@/components/ui/tab';
import { useParams } from 'next/navigation';
import rf from '@/services/RequestFactory';
import UserActivities from '@/components/user-details/UserActivitites';
import UserSetting from '@/components/user-details/UserSetting';

enum Tab {
  Info = 'info',
  Balance = 'balance',
  Open_Order = 'open_order',
  Order_History = 'order_history',
  Trade_History = 'trade_history',
  Transaction_History = 'transaction_history',
  Payment_History = 'payment_history',
  activities = 'activities',
  Settings = 'settings',
}

const tabs = [
  {
    label: 'Info',
    value: Tab.Info,
  },
  {
    label: 'Balance',
    value: Tab.Balance,
  },
  {
    label: 'Activities',
    value: Tab.activities,
  },
  {
    label: 'Open Order',
    value: Tab.Open_Order,
  },
  {
    label: 'Order History',
    value: Tab.Order_History,
  },
  {
    label: 'Trade History',
    value: Tab.Trade_History,
  },
  {
    label: 'Transaction History',
    value: Tab.Transaction_History,
  },
  {
    label: 'Payment History',
    value: Tab.Payment_History,
  },
  {
    label: 'Settings',
    value: Tab.Settings,
  },
];

const UserDetails = () => {
  const { userId } = useParams();
  const [activeTab, setActiveTab] = useState<string>(Tab.Info);
  const [userDetails, setUserDetails] = useState<any>({});

  const getUserDetails = async () => {
    if (!userId) return;
    try {
      const res = await rf.getRequest('AccountRequest').getAccountDetails(+userId);
      setUserDetails(res);
    } catch (e) {}
  };

  useEffect(() => {
    if (!userId) return;
    getUserDetails().then();
  }, [userId]);

  return (
    <div className="rounded-2xl bg-white dark:bg-white/[0.03] ">
      <div className="space-y-6">
        <div>
          <TabBar tabs={tabs} activeTab={activeTab} setActiveTab={setActiveTab} />

          <div className="rounded-b-xl border border-t-0 border-gray-200 p-6 pt-4 dark:border-gray-800">
            {activeTab === Tab.Info && <UserInfo userDetails={userDetails} />}
            {activeTab === Tab.Balance && <UserBalance />}
            {activeTab === Tab.Open_Order && <TableOpenOrder />}
            {activeTab === Tab.Order_History && <TableOrderHistories />}
            {activeTab === Tab.Trade_History && <TableTrades />}
            {activeTab === Tab.Transaction_History && <TableTransactions />}
            {activeTab === Tab.Payment_History && <TablePaymentHistory />}
            {activeTab === Tab.activities && <UserActivities />}
            {activeTab === Tab.Settings && (
              <UserSetting
                userDetails={userDetails}
                onFetchData={getUserDetails}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetails;
