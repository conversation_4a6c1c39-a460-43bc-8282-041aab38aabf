'use client';

import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import Link from 'next/link';

export default function NoAccessContent() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  return (
    <div className="flex min-h-[60vh] flex-col items-center justify-center text-center">
      <div className="mx-auto max-w-md">
        {/* Access Denied Icon */}
        <div className="mb-8 flex justify-center">
          <div className="flex h-20 w-20 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <svg
              className="h-10 w-10 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10m8-2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>

        {/* Title */}
        <h1 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white/90 lg:text-3xl">
          Access Denied
        </h1>

        {/* Description */}
        <p className="mb-6 text-gray-600 dark:text-gray-400">
          You don't have permission to access any admin pages. Please contact your administrator to request the necessary permissions.
        </p>

        {/* User Info */}
        {userInfo && userInfo.email && (
          <div className="mb-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Logged in as: <span className="font-medium text-gray-800 dark:text-white/90">{userInfo.email}</span>
            </p>
            {userInfo.roles && userInfo.roles.length > 0 && (
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Role: <span className="font-medium text-gray-800 dark:text-white/90">{userInfo.roles.join(', ')}</span>
              </p>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="space-y-3">
          <Link
            href="/profile"
            className="inline-flex w-full items-center justify-center rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-800 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
          >
            View Profile
          </Link>
          
          <button
            onClick={() => {
              if (typeof window !== 'undefined') {
                window.location.href = '/signin';
              }
            }}
            className="inline-flex w-full items-center justify-center rounded-lg bg-red-600 px-4 py-2.5 text-sm font-medium text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
          >
            Sign Out
          </button>
        </div>

        {/* Help Text */}
        <p className="mt-6 text-xs text-gray-500 dark:text-gray-400">
          If you believe this is an error, please contact your system administrator.
        </p>
      </div>
    </div>
  );
}
