'use client';
import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import rf from '@/services/RequestFactory';
import { formatUnixTimestamp } from '@/utils/format';
import { useWindowSize } from '@/hooks';

const TableUserLevels = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('TradingRequest').getTradingUserLevels(params);
      return { data: res.docs?.sort((a: any, b: any) => a.level - b.level), cursor: res.cursor };
    } catch (e: any) {
      console.error('Get Trading User Levels Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 250;
  }, [windowHeight]);

  return (
    <>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1100}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[33%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Created At
                </div>
                <div className="text-theme-xs w-[33%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  User ID
                </div>
                <div className="text-theme-xs w-[34%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Level
                </div>
              </div>
            );
          }}
          renderRow={(userLevel: any) => {
            return (
              <div
                key={userLevel.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[33%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(userLevel.createdAt)}
                </div>
                <div className="text-theme-sm w-[33%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {userLevel.userId}
                </div>
                <div className="text-theme-sm w-[34%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {userLevel.level}
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TableUserLevels;
