'use client';
import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';

import rf from '@/services/RequestFactory';
import { formatUnixTimestamp } from '@/utils/format';
import { useWindowSize } from '@/hooks';
import { TPair } from '@/types/pair';
// import ButtonAddPair from './ButtonAddPair';
// import ButtonEditPair from './ButtonEditPair';
// import ButtonDeletePair from './ButtonDeletePair';

const ListTradingPair = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const onFetchData = () => {
    if (dataTableRef.current) {
      // Force refresh the data table
      (dataTableRef.current as any)?.refresh();
    }
  };

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('TradingRequest').getPair(params);
      return { data: res?.docs, cursor: res?.cursor };
    } catch (e: any) {
      console.error('Get Trading Pair Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 320;
  }, [windowHeight]);

  return (
    <>
      {/*<div className="mb-8 flex justify-end">*/}
      {/*  <ButtonAddPair onFetchData={onFetchData} />*/}
      {/*</div>*/}
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Id
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Created At
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Pair
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Price Precision
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Quantity Precision
                </div>
                {/*<div className="text-theme-xs w-[16%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">*/}
                {/*  Action*/}
                {/*</div>*/}
              </div>
            );
          }}
          renderRow={(pair: TPair) => {
            return (
              <div
                key={pair.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {pair.id || '--'}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(pair.createdAt) || '--'}
                </div>
                <div className="flex w-[20%] items-center gap-1 px-4 py-3 text-start">
                  <div className="text-theme-sm text-gray-500 dark:text-gray-400">
                    {pair.baseAsset || '--'}
                  </div>
                  <div className="text-theme-sm text-gray-500 dark:text-gray-400">/</div>
                  <div className="text-theme-sm text-gray-500 dark:text-gray-400">
                    {pair.quoteAsset || '--'}
                  </div>
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {pair.pricePrecision || '--'}
                </div>
                <div className="text-theme-sm flex w-[20%] items-center px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {pair.quantityPrecision || '--'}
                </div>

                {/*<div className="text-theme-sm flex w-[16%] items-center gap-2 px-4 py-3 text-start text-gray-500 dark:text-gray-400">*/}
                {/*  <ButtonEditPair onFetchData={onFetchData} />*/}
                {/*  <ButtonDeletePair onFetchData={onFetchData} pairId={pair?.id} />*/}
                {/*</div>*/}
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default ListTradingPair;
