'use client';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import { formatNumber, formatUnixTimestamp } from '@/utils/format';
import rf from '@/services/RequestFactory';
import { filterParams } from '@/utils/helper';
import { ChevronDownIcon, RefreshIcon } from '@/icons';
import Label from '@/components/form/Label';
import Select from '@/components/form/Select';
import { useWindowSize } from '@/hooks/useWindowSize';
import BigNumber from 'bignumber.js';
import { EOrderSide, TTradeHistory } from '@/types/order';
import SearchInput from '@/components/ui/search/index';
import DatePicker from '@/components/form/date-picker';
import moment from 'moment';

const OPTIONS_SIDE = [
  { value: '', label: 'All' },
  { value: 'Buy', label: 'BUY' },
  { value: 'Sell', label: 'SELL' },
];

const TableTrades = () => {
  const [side, setSide] = useState<string>('');
  const [userId, setUserId] = useState<string>('');
  const [symbol, setSymbol] = useState<string>('');
  const [dates, setDates] = useState<Date[]>([]);
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const getData = useCallback(
    async (params: any) => {
      try {
        const result = await rf.getRequest('TradingRequest').getTrades(
          filterParams({
            ...params,
            side,
            userId: +userId,
            symbol,
            startTime: dates[0] ? moment(dates[0]).valueOf() : null,
            endTime: dates[1] ? moment(dates[1]).valueOf() : null,
          })
        );

        return {
          cursor: result?.cursor,
          data: result?.docs || [],
        };
      } catch (err) {
        console.log(err, 'get Withdraw Histories Wallet error');
        return { cursor: null, data: [] };
      }
    },
    [side, userId, symbol, dates]
  );

  const onRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 350;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <div className="flex items-end gap-x-6">
          <div>
            <Label>Side</Label>
            <div className="relative">
              <Select
                options={OPTIONS_SIDE}
                defaultValue={side}
                onChange={(value: string) => setSide(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <DatePicker
            mode="range"
            id="date-picker"
            label="Select time"
            placeholder="Select a date"
            onChange={(dates: Date[], currentDateString: string) => {
              console.log({ dates, currentDateString });
              setDates(dates);
            }}
          />
          <SearchInput setSearch={setUserId} placeholder="Search by User ID" />
          <SearchInput setSearch={setSymbol} placeholder="Search by Symbol" />
        </div>

        <div onClick={onRefreshData} className="cursor-pointer text-gray-800 dark:text-white/90">
          <RefreshIcon />
        </div>
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          height={tableHeight}
          minWidth={1250}
          ref={dataTableRef}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Date
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Pair
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Side
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Price
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Amount
                </div>
                <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Executed
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Maker Fee / Taker Fee
                </div>
              </div>
            );
          }}
          renderRow={(order: TTradeHistory) => {
            return (
              <div
                key={order.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[15%] items-center px-4 py-3 text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(order.createdAt)}
                </div>
                <div className="text-theme-sm flex w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {order.baseAssetSymbol}/{order.quoteAssetSymbol}
                </div>
                <div className="text-theme-sm flex w-[10%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <span className={order.isBuyerMaker ? 'text-green-400' : 'text-red-400'}>
                    {order.isBuyerMaker ? EOrderSide.BUY : EOrderSide.SELL}
                  </span>
                </div>
                <div className="text-theme-sm flex w-[15%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {new BigNumber(order.price).toFormat()}
                </div>
                <div className="text-theme-sm flex w-[15%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {new BigNumber(order.quoteQty).toFormat()}
                </div>
                <div className="text-theme-sm flex w-[15%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {formatNumber(order.qty)}
                </div>
                <div className="text-theme-sm flex w-[20%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {formatNumber(order.makerFee)} / {formatNumber(order.takerFee)}
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TableTrades;
