'use client';
import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import rf from '@/services/RequestFactory';
import { formatUnixTimestamp } from '@/utils/format';
import { useWindowSize } from '@/hooks';
import { formatNumber } from '../../../../../utils/format';

const TableDailyVolumes = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('TradingRequest').getTradingDailyVolumes(params);
      return { data: res.docs, cursor: res.cursor };
    } catch (e: any) {
      console.error('Get Trading Daily Volumes Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 250;
  }, [windowHeight]);

  return (
    <>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1100}
          ref={dataTableRef}
          height={tableHeight}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  User ID
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Trade Time
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Volume USDT
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Trades Count
                </div>
                <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Snapshot At
                </div>
              </div>
            );
          }}
          renderRow={(user: any, index: number) => {
            return (
              <div
                key={index}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {user.userId}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {user.tradeDate}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatNumber(user.usdtVolume)}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {user.tradesCount}
                </div>
                <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(user.createdAt * 1000)}
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TableDailyVolumes;
