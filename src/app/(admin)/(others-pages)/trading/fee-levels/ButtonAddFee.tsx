'use client';

import React from 'react';

import Button from '@/components/ui/button/Button';
import { useModal } from '@/hooks/useModal';
import ModalAddFeeLevel from '@/modals/ModalAddFeeLevel';

const ButtonAddFee = ({ onFetchData }: { onFetchData: () => void }) => {
  const { isOpen, openModal, closeModal } = useModal();
  return (
    <>
      <Button disabled onClick={openModal} size="sm" variant="primary">
        Add New Fee Level
      </Button>

      {isOpen && (
        <ModalAddFeeLevel isOpen={isOpen} onClose={closeModal} onFetchData={onFetchData} />
      )}
    </>
  );
};

export default ButtonAddFee;
