'use client';

import React from 'react';

import Button from '@/components/ui/button/Button';
import { useModal } from '@/hooks/useModal';
import ModalAddMarket from '@/modals/ModalAddMarket';

const ButtonAddMarket = ({ onFetchData }: { onFetchData: () => void }) => {
  const { isOpen, openModal, closeModal } = useModal();
  return (
    <>
      <Button disabled onClick={openModal} size="sm" variant="primary">
        Add New Market
      </Button>

      {isOpen && <ModalAddMarket isOpen={isOpen} onClose={closeModal} onFetchData={onFetchData} />}
    </>
  );
};

export default ButtonAddMarket;
