'use client';

import React, { useMemo, useRef } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import { formatUnixTimestamp } from '@/utils/format';
import Image from 'next/image';
import { useWindowSize } from '@/hooks';
import { TToken } from '@/types/token';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/index';
import { useModal } from '@/hooks/useModal';
import ModalTokenDetails from '@/modals/ModalTokenDetails';

const TokenItem = ({ token }: { token: TToken }) => {
  const { isOpen, openModal, closeModal } = useModal();
  return (
    <>
      <div
        onClick={openModal}
        key={token.id}
        className="flex w-full cursor-pointer items-center border-b border-gray-100 dark:border-white/[0.05]"
      >
        <div className="text-theme-sm w-[20%] px-4 py-3 text-gray-500 dark:text-gray-400">
          {token.id}
        </div>
        <div className="text-theme-sm w-[20%] px-4 py-3 text-gray-500 dark:text-gray-400">
          {formatUnixTimestamp(token.createdAt)}
        </div>
        <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-3">
            {token.logoUrl && (
              <div className="h-8 w-8 overflow-hidden rounded-full">
                <Image width={32} height={32} src={token.logoUrl} alt={token.symbol} />
              </div>
            )}
            <span>{token.name || '--'}</span>
          </div>
        </div>
        <div className="text-theme-sm w-[20%] px-4 py-3 text-start uppercase text-gray-500 dark:text-gray-400">
          {token.symbol || '--'}
        </div>
        <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {token.internalWithdrawEnabled ? 'Enabled' : 'Disabled'}
        </div>
      </div>
      <ModalTokenDetails isOpen={isOpen} onClose={closeModal} token={token} />
    </>
  );
};

const ListToken = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const { windowHeight } = useWindowSize();

  const getData = async () => {
    return {
      data: assets || [],
    };
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 250;
  }, [windowHeight]);

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
      <AppDataTable
        minWidth={1102}
        ref={dataTableRef}
        getData={getData as any}
        height={tableHeight}
        renderHeader={() => {
          return (
            <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
              <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                ID
              </div>
              <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Created At
              </div>
              <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Name
              </div>
              <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Symbol
              </div>
              <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Internal Withdraw
              </div>
            </div>
          );
        }}
        renderRow={(token: TToken) => {
          return <TokenItem token={token} key={token.id} />;
        }}
      />
    </div>
  );
};

export default ListToken;
