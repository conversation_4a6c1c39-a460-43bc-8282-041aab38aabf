'use client';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import Badge from '@/components/ui/badge/Badge';
import rf from '@/services/RequestFactory';
import { useWindowSize } from '@/hooks';
import { ChevronDownIcon, SearchIcon } from '@/icons';
import Select from '@/components/form/Select';
import Label from '@/components/form/Label';
import { filterParams } from '@/utils/helper';
import { formatShortAddress, formatUnixTimestamp } from '@/utils/format';

const OPTIONS_LEVEL = [
  { value: 'all', label: 'All' },
  { value: 'basic', label: 'Basic' },
  { value: 'advance', label: 'Advance' },
];

const TableKYCVerificationRejected = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();
  const [name, setName] = useState<string>('');
  const [levelName, setLevelName] = useState<string>('all');

  const getData = useCallback(
    async (params: any) => {
      try {
        const res = await rf.getRequest('AccountRequest').getKycsRejected(
          filterParams({
            ...params,
            levelName,
            name,
          })
        );
        return { data: res?.docs, cursor: res?.cursor };
      } catch (e: any) {
        console.error('Get Kyc Error', e?.message);
        return { data: [], cursor: null };
      }
    },
    [levelName, name]
  );

  const tableHeight = useMemo(() => {
    return windowHeight - 450;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-4 flex items-end justify-between gap-x-6">
        <div className="flex items-end gap-x-6">
          <div>
            <Label>Level</Label>
            <div className="relative">
              <Select
                options={OPTIONS_LEVEL}
                defaultValue={levelName}
                onChange={(value: string) => setLevelName(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <div className="relative">
            <span className="pointer-events-none absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
              <SearchIcon />
            </span>
            <input
              value={name}
              onChange={(e) => setName(e?.target?.value?.trim())}
              type="text"
              placeholder="Search by Name"
              className="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full truncate rounded-lg border border-gray-200 bg-transparent py-2.5 pl-12 pr-3 text-sm text-gray-800 placeholder:text-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:bg-white/[0.03] dark:text-white/90 dark:placeholder:text-white/30"
            />
          </div>
        </div>
      </div>
      <div className="overflow-hidden">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          getData={getData as any}
          height={tableHeight}
          renderHeader={() => (
            <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
              <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                User ID
              </div>
              <div className="text-theme-xs w-[14%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Applicant Id
              </div>
              <div className="text-theme-xs w-[14%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Level Name
              </div>
              <div className="text-theme-xs w-[17%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Processed At
              </div>
              <div className="text-theme-xs w-[14%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Review Status
              </div>
              <div className="text-theme-xs w-[14%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Review Answer
              </div>
              <div className="text-theme-xs w-[17%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Created At
              </div>
            </div>
          )}
          renderRow={(kyc: any) => (
            <div
              key={kyc.userId}
              className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
            >
              <div className="text-theme-sm w-[10%] px-4 py-3 text-gray-500 dark:text-gray-400">
                {kyc.userId || '--'}
              </div>
              <div className="text-theme-sm w-[14%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                {formatShortAddress(kyc?.applicantId)}
              </div>
              <div className="text-theme-sm w-[14%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                {kyc.levelName}
              </div>
              <div className="text-theme-sm w-[17%]  px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                {formatUnixTimestamp(kyc.processedAt * 1000)}
              </div>
              <div className="text-theme-sm flex w-[14%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                <Badge size="sm" color={kyc.reviewStatus === 'completed' ? 'success' : 'error'}>
                  {kyc.reviewStatus || '--'}
                </Badge>
              </div>
              <div className="text-theme-sm w-[14%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
                <Badge
                  size="sm"
                  color={'error'}
                >
                  {kyc.reviewAnswer || '--'}
                </Badge>
              </div>
              <div className="text-theme-sm w-[17%] px-4 py-3 text-gray-500 dark:text-gray-400">
                {formatUnixTimestamp(kyc.createdAt * 1000)}
              </div>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableKYCVerificationRejected;
