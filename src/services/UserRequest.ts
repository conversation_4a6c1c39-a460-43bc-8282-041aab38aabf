import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class UserRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getUserBalance(params: any) {
    const url = `/user-activity/balance`;
    return this.get(url, params);
  }

  getUserActivities(params: any) {
    const url = `/user-activity/activities`;
    return this.get(url, params);
  }
}
