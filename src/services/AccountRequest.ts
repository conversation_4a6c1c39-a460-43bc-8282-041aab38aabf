import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class AccountRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getAccounts(params: any) {
    const url = `/account`;
    return this.get(url, { ...params });
  }

  getAccountDetails(userId: number) {
    const url = `/account/${userId}`;
    return this.get(url);
  }

  getKYCDetails(userId: number) {
    const url = `/account/kycs/${userId}`;
    return this.get(url);
  }

  getTransactionHistories(params: any) {
    const url = `/account/transaction-histories`;
    return this.get(url, params);
  }

  getKycs(params: any) {
    const url = `/account/kycs`;
    return this.get(url, { ...params });
  }

  getKycsApproved(params: any) {
    const url = `/account/kycs/approved`;
    return this.get(url, { ...params });
  }

  getKycsPending(params: any) {
    const url = `/account/kycs/pending`;
    return this.get(url, { ...params });
  }

  getKycsRejected(params: any) {
    const url = `/account/kycs/rejected`;
    return this.get(url, { ...params });
  }

  enableUserWithdrawal(params: any) {
    const url = `/account/enable-user-withdrawal`;
    return this.patch(url, params);
  }

  disableUserWithdrawal(params: any) {
    const url = `/account/disable-user-withdrawal`;
    return this.patch(url, params);
  }

  enableUserDeposit(params: any) {
    const url = `/account/enable-user-deposit`;
    return this.patch(url, params);
  }

  disableUserDeposit(params: any) {
    const url = `/account/disable-user-deposit`;
    return this.patch(url, params);
  }
}
