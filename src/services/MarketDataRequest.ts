import config from '@/config';
import BaseRootRequest from './BaseRequest';

export default class MarketDataRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiDexUrl;
  }

  getAssets() {
    const url = `/assets`;
    return this.get(url);
  }

  getNetworks() {
    const url = `/networks`;
    return this.get(url);
  }

  getAssetConfig(params: { assetId: number; type?: string }) {
    const url = `/crypto-assets`;
    return this.get(url, params);
  }
}
