import { AppDrawer } from '@/components/ui/modal/AppDrawer';
import React, { useEffect, useState } from 'react';
import { TToken } from '@/types/token';
import Image from 'next/image';
import { TAssetConfig } from '@/types/asset';
import rf from '@/services/RequestFactory';
import { formatShortAddress } from '@/utils/format';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/index';
import { copyToClipboard } from '@/utils/helper';
import { CopyIcon, EditIcon } from '@/icons';
import Switch from '../components/form/switch/Switch';
import { ChevronDownIcon } from '../icons/index';
import { toastError, toastSuccess } from '../libs/toast';
import Button from '@/components/ui/button/Button';
import { useForm } from 'react-hook-form';
import BigNumber from 'bignumber.js';
import Input from '../components/form/input/InputField';
import { getAssets } from '@/store/metadata.store';
import { store } from '@/store';
import { sleep } from '@/utils/helper';

const NetworkConfiguration = ({
  config,
  token,
  onFetchData,
}: {
  config: TAssetConfig;
  token: TToken;
  onFetchData: () => void;
}) => {
  const [isShowFull, setIsShowFull] = useState<boolean>(false);
  const networks = useSelector((state: RootState) => state.metadata.networks);
  const network = networks.find((n) => n.id === config.networkId);
  const [isLoadingDeposit, setIsLoadingDeposit] = useState<boolean>(false);
  const [isLoadingWithdraw, setIsLoadingWithdraw] = useState<boolean>(false);

  const [editType, setEditType] = useState<'none' | 'deposit' | 'withdraw'>('none');

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm({
    defaultValues: {
      depositFee: config.depositFee,
      withdrawFee: config.withdrawFee,
    },
  });

  useEffect(() => {
    reset({
      depositFee: new BigNumber(config.depositFee).toFixed(),
      withdrawFee: new BigNumber(config.withdrawFee).toFixed(),
    });
  }, [config, reset]);

  const onSubmit = async (data: any) => {
    try {
      if (editType === 'deposit') {
        await rf.getRequest('TradingRequest').editDepositFee({
          asset_id: token.id,
          network_id: config.networkId,
          deposit_fee: data.depositFee,
        });
        toastSuccess('Success', 'Deposit fee updated!');
      } else if (editType === 'withdraw') {
        await rf.getRequest('TradingRequest').editwithdrawFee({
          asset_id: token.id,
          network_id: config.networkId,
          withdraw_fee: data.withdrawFee,
        });
        toastSuccess('Success', 'Withdraw fee updated!');
      }
      setEditType('none');
      onFetchData();
    } catch (e: any) {
      toastError('Error', e?.message || 'Something went wrong!');
    }
  };

  const handleEdit = (type: 'deposit' | 'withdraw') => {
    setEditType(type);
    if (type === 'deposit') setValue('depositFee', config.depositFee);
    if (type === 'withdraw') setValue('withdrawFee', config.withdrawFee);
  };

  const handleCancel = () => {
    setEditType('none');
    reset({
      depositFee: config.depositFee,
      withdrawFee: config.withdrawFee,
    });
  };

  const handleChangeConfigWithdraw = async () => {
    if (isLoadingWithdraw) return;
    try {
      setIsLoadingWithdraw(true);
      if (config.withdrawEnabled) {
        await rf.getRequest('TradingRequest').disableAssetWithdrawal({
          assetId: token.id,
          networkId: config.networkId,
        });
        toastSuccess('Success', 'Disable Successfully!');
      } else {
        await rf.getRequest('TradingRequest').enableAssetWithdrawal({
          assetId: token.id,
          networkId: config.networkId,
        });
        toastSuccess('Success', 'Enable Successfully!');
      }
      await sleep(2000);
      onFetchData();
      setIsLoadingWithdraw(false);
    } catch (e: any) {
      setIsLoadingWithdraw(false);
      toastError('Error', e?.message || 'Something went wrong!');
      console.error('Config Withdraw', e?.message);
    }
  };

  const handleChangeConfigDeposit = async () => {
    if (isLoadingDeposit) return;
    try {
      setIsLoadingDeposit(true);
      if (config.depositEnabled) {
        await rf.getRequest('TradingRequest').disableAssetDeposit({
          assetId: token.id,
          networkId: config.networkId,
        });
        toastSuccess('Success', 'Disable Successfully!');
      } else {
        await rf.getRequest('TradingRequest').enableAssetDeposit({
          assetId: token.id,
          networkId: config.networkId,
        });
        toastSuccess('Success', 'Enable Successfully!');
      }
      await sleep(2000);
      onFetchData();
      setIsLoadingDeposit(false);
    } catch (e: any) {
      setIsLoadingDeposit(false);
      toastError('Error', e?.message || 'Something went wrong!');
      console.error('Config Withdraw', e?.message);
    }
  };

  return (
    <div className="rounded-2xl border border-gray-100 p-4 dark:border-white/[0.05] ">
      <div
        className="flex w-full cursor-pointer items-center justify-between gap-2"
        onClick={() => setIsShowFull(!isShowFull)}
      >
        <div className="flex items-center gap-2">
          {network?.logoUrl && (
            <div className="h-6 w-6 overflow-hidden rounded-full">
              <img width={24} height={24} src={network?.logoUrl} alt={network?.name} />
            </div>
          )}
          <div className="text-lg">{network?.name}</div>
        </div>

        <ChevronDownIcon
          className={`ml-auto h-5 w-5 transition-transform duration-200  ${
            isShowFull ? 'rotate-180' : ''
          }`}
        />
      </div>
      {isShowFull && (
        <>
          <div className="my-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">Contract Address</div>
            <div className="flex items-center gap-1">
              {formatShortAddress(config.contractAddress)}
              {config.contractAddress && (
                <CopyIcon
                  onClick={() => copyToClipboard(config.contractAddress)}
                  className="cursor-pointer"
                />
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
            <div className="col-span-2 lg:col-span-1">
              <div className="text-sm text-gray-500 dark:text-gray-400">Minimum Withdraw</div>
              <div>
                {config.minWithdraw} {token.symbol}
              </div>
            </div>
            <div className="col-span-2 lg:col-span-1">
              <div className="text-sm text-gray-500 dark:text-gray-400">Minimum Deposit</div>
              <div>
                {config.minDeposit} {token.symbol}
              </div>
            </div>
            <div className="col-span-2 lg:col-span-1">
              <div className="flex items-center gap-2">
                <div className="text-sm text-gray-500 dark:text-gray-400">Withdrawal Fee</div>
                <div
                  className="h-[14px] w-[14px] cursor-pointer"
                  onClick={() => handleEdit('withdraw')}
                >
                  <EditIcon />
                </div>
              </div>
              <div>
                {editType === 'withdraw' ? (
                  <form onSubmit={handleSubmit(onSubmit)} className="my-2">
                    <Input
                      {...register('withdrawFee', { required: true })}
                      type="number"
                      step="any"
                      error={!!errors.withdrawFee}
                    />
                    {errors.withdrawFee && (
                      <p className="text-xs text-red-400">This field is required</p>
                    )}
                    <div className="mt-2 grid grid-cols-2 gap-3">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancel}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button size="sm" type="submit" disabled={isSubmitting}>
                        Save
                      </Button>
                    </div>
                  </form>
                ) : (
                  <>
                    {config.withdrawFee} {token.symbol}
                  </>
                )}
              </div>
            </div>
            <div className="col-span-2 lg:col-span-1">
              <div className="flex items-center gap-3">
                <div className="text-sm text-gray-500 dark:text-gray-400">Deposit Fee</div>
                <div
                  className="h-[14px] w-[14px] cursor-pointer"
                  onClick={() => handleEdit('deposit')}
                >
                  <EditIcon />
                </div>
              </div>
              <div>
                {editType === 'deposit' ? (
                  <form onSubmit={handleSubmit(onSubmit)} className="my-2">
                    <Input
                      {...register('depositFee', { required: true })}
                      type="number"
                      step="any"
                      error={!!errors.depositFee}
                    />
                    {errors.depositFee && (
                      <p className="text-xs text-red-400">This field is required</p>
                    )}
                    <div className="mt-2 grid grid-cols-2 gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCancel}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button size="sm" type="submit" disabled={isSubmitting}>
                        Save
                      </Button>
                    </div>
                  </form>
                ) : (
                  <>
                    {config.depositFee} {token.symbol}
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 flex flex-col gap-4 border-t border-gray-100 pt-4 text-sm text-gray-500 dark:border-white/[0.05] dark:text-gray-400">
            <div className="flex items-center justify-between">
              <div className="text-sm">Deposit Enabled</div>
              <div className="flex items-center gap-2">
                {isLoadingDeposit && (
                  <div className="border-brand-500 h-4 w-4 animate-spin rounded-full border-b-2"></div>
                )}
                <Switch value={config.depositEnabled} onChange={handleChangeConfigDeposit} />
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div className="text-sm">Withdraw Enabled</div>

              <div className="flex items-center gap-2">
                {isLoadingWithdraw && (
                  <div className="border-brand-500 h-4 w-4 animate-spin rounded-full border-b-2"></div>
                )}
                <Switch value={config.withdrawEnabled} onChange={handleChangeConfigWithdraw} />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

const ModalTokenDetails = ({
  isOpen,
  onClose,
  token,
}: {
  isOpen: boolean;
  onClose: () => void;
  token: TToken;
}) => {
  const [assetConfig, setAssetConfig] = useState<TAssetConfig[]>([]);

  const getAssetConfig = async () => {
    if (!token?.id) return;
    try {
      const data = await rf.getRequest('MarketDataRequest').getAssetConfig({
        assetId: token?.id,
      });
      setAssetConfig(data?.data);
    } catch (e: any) {
      console.error(e?.message, 'Get Asset Config Error');
    }
  };

  const handleChangeConfigWithdrawInternal = async () => {
    try {
      if (token.internalWithdrawEnabled) {
        await rf.getRequest('TradingRequest').disableAssetWithdrawalInternal({
          assetId: token.id,
        });
        toastSuccess('Success', 'Disabled Successfully!');
      } else {
        await rf.getRequest('TradingRequest').enableAssetWithdrawalInternal({
          assetId: token.id,
        });
        toastSuccess('Success', 'Enabled Successfully!');
      }
      store.dispatch(getAssets());
    } catch (e: any) {
      toastError('Error', e?.message || 'Something went wrong!');
      console.error('Config Internal Withdraw', e?.message);
    }
  };

  useEffect(() => {
    if (!token?.id || !isOpen) return;
    getAssetConfig().then();
  }, [token?.id, isOpen]);

  return (
    <AppDrawer isOpen={isOpen} toggleDrawer={onClose} className="lg:!w-[550px] ">
      <div className="flex items-center justify-between border-b border-gray-100 p-4 dark:border-white/[0.05]">
        Token Details
        <button onClick={onClose}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M6.04289 16.5413C5.65237 16.9318 5.65237 17.565 6.04289 17.9555C6.43342 18.346 7.06658 18.346 7.45711 17.9555L11.9987 13.4139L16.5408 17.956C16.9313 18.3466 17.5645 18.3466 17.955 17.956C18.3455 17.5655 18.3455 16.9323 17.955 16.5418L13.4129 11.9997L17.955 7.4576C18.3455 7.06707 18.3455 6.43391 17.955 6.04338C17.5645 5.65286 16.9313 5.65286 16.5408 6.04338L11.9987 10.5855L7.45711 6.0439C7.06658 5.65338 6.43342 5.65338 6.04289 6.0439C5.65237 6.43442 5.65237 7.06759 6.04289 7.45811L10.5845 11.9997L6.04289 16.5413Z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>

      <div className="flex items-center gap-3 p-4">
        {token.logoUrl && (
          <div className="h-8 w-8 overflow-hidden rounded-full">
            <Image width={32} height={32} src={token.logoUrl} alt={token.symbol} />
          </div>
        )}
        <div>
          <div>{token.symbol || '--'}</div>
          <div className="text-theme-xs text-gray-500 dark:text-gray-400">{token.name || '--'}</div>
        </div>
      </div>

      <div className="mx-4 mb-4 flex items-center justify-between">
        <div className="text-sm text-gray-500 dark:text-gray-400">Withdrawal Internal Enabled</div>
        <Switch
          value={token.internalWithdrawEnabled}
          onChange={handleChangeConfigWithdrawInternal}
        />
      </div>

      <div className="px-4 text-lg text-gray-500 dark:text-gray-400">Network Configuration</div>

      <div className="customer-scroll m-4 flex h-[calc(100vh-220px)] flex-col gap-4 overflow-y-auto">
        {assetConfig.map((config, index) => {
          return (
            <NetworkConfiguration
              key={index}
              config={config}
              token={token}
              onFetchData={getAssetConfig}
            />
          );
        })}
      </div>
    </AppDrawer>
  );
};

export default ModalTokenDetails;
