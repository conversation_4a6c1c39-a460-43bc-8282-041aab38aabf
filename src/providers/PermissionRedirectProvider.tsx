'use client';

import { ReactNode, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { canAccessPage } from '@/utils/permissions';
import { getRoutePermissions, generateRedirectOrder } from '@/config/navigationConfig';

interface PermissionRedirectProviderProps {
  children: ReactNode;
}

export default function PermissionRedirectProvider({ children }: PermissionRedirectProviderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const isUserLoading = useSelector((state: RootState) => state.user.loading);

  // Get navigation order from centralized configuration
  const navigationOrder = generateRedirectOrder();

  useEffect(() => {
    if (isUserLoading || !userInfo || !userInfo.admin_id) {
      return;
    }

    const requiredPermissions = getRoutePermissions(pathname);

    if (!requiredPermissions) {
      return;
    }

    // Check if user can access current page
    const canAccess = canAccessPage(userInfo, requiredPermissions);

    if (canAccess) {
      return;
    }

    const firstAccessiblePage = navigationOrder.find((item) =>
      canAccessPage(userInfo, item.permissions)
    );

    if (firstAccessiblePage) {
      router.replace(firstAccessiblePage.path);
    } else {
      router.replace('/');
    }
  }, [pathname, userInfo, isUserLoading, router]);

  return <>{children}</>;
}
