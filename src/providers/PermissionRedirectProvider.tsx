'use client';

import { ReactNode, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { canAccessPage } from '@/utils/permissions';
import { getRoutePermissions } from '@/config/routePermissions';
import { PermissionEnum } from '@/types/enums';

interface PermissionRedirectProviderProps {
  children: ReactNode;
}

export default function PermissionRedirectProvider({ children }: PermissionRedirectProviderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const isUserLoading = useSelector((state: RootState) => state.user.loading);

  const navigationOrder = [
    { path: '/', permissions: [PermissionEnum.DASHBOARD] },
    { path: '/users', permissions: [PermissionEnum.USERS] },
    { path: '/kyc', permissions: [PermissionEnum.KYC] },
    { path: '/wallets/cold', permissions: [PermissionEnum.WALLETS_COLD] },
    { path: '/wallets/hot', permissions: [PermissionEnum.WALLETS_HOT] },
    { path: '/wallets/deposit-wallets', permissions: [PermissionEnum.WALLETS_DEPOSIT] },
    { path: '/wallets/transactions', permissions: [PermissionEnum.WALLETS_TRANSACTIONS] },
    { path: '/exchange/transactions', permissions: [PermissionEnum.TRANSACTIONS] },
    { path: '/exchange/payments', permissions: [PermissionEnum.TRANSACTIONS_PAYMENTS] },
    { path: '/trading/open-orders', permissions: [PermissionEnum.TRADING_OPEN_ORDERS] },
    { path: '/trading/order-histories', permissions: [PermissionEnum.TRADING_ORDER_HISTORY] },
    { path: '/trading/trades', permissions: [PermissionEnum.TRADING_TRADES] },
    { path: '/trading/daily-volumes', permissions: [PermissionEnum.TRADING_DAILY_VOLUMES] },
    { path: '/trading/user-levels', permissions: [PermissionEnum.TRADING_USER_LEVELS] },
    { path: '/trading/markets', permissions: [PermissionEnum.TRADING_MARKETS] },
    { path: '/trading/pairs', permissions: [PermissionEnum.TRADING_PAIRS] },
    { path: '/trading/fee-levels', permissions: [PermissionEnum.TRADING_FEE_LEVELS] },
    { path: '/tokens', permissions: [PermissionEnum.TOKENS] },
    { path: '/admins', permissions: [PermissionEnum.ADMINS] },
    { path: '/roles', permissions: [PermissionEnum.ROLES] },
  ];

  useEffect(() => {
    if (isUserLoading || !userInfo || !userInfo.admin_id) {
      return;
    }

    const requiredPermissions = getRoutePermissions(pathname);

    if (!requiredPermissions) {
      return;
    }

    // Check if user can access current page
    const canAccess = canAccessPage(userInfo, requiredPermissions);

    if (canAccess) {
      return;
    }

    const firstAccessiblePage = navigationOrder.find((item) =>
      canAccessPage(userInfo, item.permissions)
    );

    if (firstAccessiblePage) {
      router.replace(firstAccessiblePage.path);
    } else {
      router.replace('/');
    }
  }, [pathname, userInfo, isUserLoading, router]);

  return <>{children}</>;
}
