import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { COOKIES_ACCESS_TOKEN_KEY } from '@/constants';

export async function middleware(request: NextRequest) {
  const token = request.cookies.get(COOKIES_ACCESS_TOKEN_KEY)?.value;

  const { pathname } = request.nextUrl;
  const allowedPaths = ['/signin', '/signup', '/welcome', '/error-404'];

  const isAllowedPath = allowedPaths.some((path) => pathname.startsWith(path));

  if (!token && !isAllowedPath) {
    return NextResponse.redirect(new URL('/signin', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api/|_next/|static/|public/|signin|signup|welcome|error-404|logo/|image|favicon.ico|opengraph-image.png).*)',
  ],
};
