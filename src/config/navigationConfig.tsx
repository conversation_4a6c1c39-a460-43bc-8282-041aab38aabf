import React from 'react';
import { PermissionEnum } from '@/types/enums';
import {
  GridIcon,
  UserCircleIcon,
  KYCIcon,
  ListIcon,
  TableIcon,
  BoxCubeIcon,
  GroupIcon,
} from '@/icons';

/**
 * Central navigation configuration - Single source of truth
 * This configuration drives:
 * 1. Sidebar navigation (AppSidebar)
 * 2. Route permissions (routePermissions)
 * 3. Permission redirect logic (PermissionRedirectProvider)
 */

export interface NavigationItem {
  name: string;
  icon?: React.ReactNode;
  path?: string;
  requiredPermissions?: PermissionEnum[];
  subItems?: NavigationSubItem[];
  // UI-specific properties
  pro?: boolean;
  new?: boolean;
}

export interface NavigationSubItem {
  name: string;
  path: string;
  requiredPermissions?: PermissionEnum[];
  pro?: boolean;
  new?: boolean;
}

/**
 * Master navigation configuration
 * This is the single source of truth for all navigation, routing, and permissions
 */
export const navigationConfig: NavigationItem[] = [
  {
    name: 'Dashboard',
    icon: <GridIcon />,
    path: '/',
    requiredPermissions: [PermissionEnum.DASHBOARD],
  },
  {
    name: 'User Management',
    icon: <UserCircleIcon />,
    path: '/users',
    requiredPermissions: [PermissionEnum.USERS],
  },
  {
    name: 'KYC Verification',
    icon: <KYCIcon />,
    path: '/kyc',
    requiredPermissions: [PermissionEnum.KYC],
  },
  {
    name: 'Wallet Management',
    icon: <ListIcon />,
    requiredPermissions: [PermissionEnum.WALLETS],
    subItems: [
      {
        name: 'Cold Wallets',
        path: '/wallets/cold',
        requiredPermissions: [PermissionEnum.WALLETS_COLD],
        pro: false,
      },
      {
        name: 'Hot Wallets',
        path: '/wallets/hot',
        requiredPermissions: [PermissionEnum.WALLETS_HOT],
        pro: false,
      },
      {
        name: 'Deposit Wallets',
        path: '/wallets/deposit-wallets',
        requiredPermissions: [PermissionEnum.WALLETS_DEPOSIT],
        pro: false,
      },
      {
        name: 'Transaction History',
        path: '/wallets/transactions',
        requiredPermissions: [PermissionEnum.WALLETS_TRANSACTIONS],
        pro: false,
      },
    ],
  },
  {
    name: 'Transactions Monitoring',
    icon: <TableIcon />,
    requiredPermissions: [PermissionEnum.TRANSACTIONS, PermissionEnum.TRANSACTIONS_PAYMENTS],
    subItems: [
      {
        name: 'Transactions History',
        path: '/exchange/transactions',
        requiredPermissions: [PermissionEnum.TRANSACTIONS],
        pro: false,
      },
      {
        name: 'Payment History',
        path: '/exchange/payments',
        requiredPermissions: [PermissionEnum.TRANSACTIONS_PAYMENTS],
        pro: false,
      },
    ],
  },
  {
    name: 'Trading Monitoring',
    icon: <TableIcon />,
    path: '/trading',
    requiredPermissions: [
      PermissionEnum.TRADING_OPEN_ORDERS,
      PermissionEnum.TRADING_ORDER_HISTORY,
      PermissionEnum.TRADING_TRADES,
      PermissionEnum.TRADING_FEE_REVENUE,
      PermissionEnum.TRADING_DAILY_VOLUMES,
      PermissionEnum.TRADING_USER_LEVELS,
    ],
    subItems: [
      {
        name: 'Open Orders',
        path: '/trading/open-orders',
        requiredPermissions: [PermissionEnum.TRADING_OPEN_ORDERS],
        pro: false,
      },
      {
        name: 'Order History',
        path: '/trading/order-histories',
        requiredPermissions: [PermissionEnum.TRADING_ORDER_HISTORY],
        pro: false,
      },
      {
        name: 'Trades',
        path: '/trading/trades',
        requiredPermissions: [PermissionEnum.TRADING_TRADES],
        pro: false,
      },
      {
        name: 'Daily Volumes',
        path: '/trading/daily-volumes',
        requiredPermissions: [PermissionEnum.TRADING_DAILY_VOLUMES],
        pro: false,
      },
      {
        name: 'User Levels',
        path: '/trading/user-levels',
        requiredPermissions: [PermissionEnum.TRADING_USER_LEVELS],
        pro: false,
      },
    ],
  },
  {
    name: 'Trading Settings',
    icon: (
      <svg
        className="fill-gray-500 group-hover:fill-gray-700 dark:fill-gray-400 dark:group-hover:fill-gray-300"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.4858 3.5L13.5182 3.5C13.9233 3.5 14.2518 3.82851 14.2518 4.23377C14.2518 5.9529 16.1129 7.02795 17.602 6.1682C17.9528 5.96567 18.4014 6.08586 18.6039 6.43667L20.1203 9.0631C20.3229 9.41407 20.2027 9.86286 19.8517 10.0655C18.3625 10.9253 18.3625 13.0747 19.8517 13.9345C20.2026 14.1372 20.3229 14.5859 20.1203 14.9369L18.6039 17.5634C18.4013 17.9142 17.9528 18.0344 17.602 17.8318C16.1129 16.9721 14.2518 18.0471 14.2518 19.7663C14.2518 20.1715 13.9233 20.5 13.5182 20.5H10.4858C10.0804 20.5 9.75182 20.1714 9.75182 19.766C9.75182 18.0461 7.88983 16.9717 6.40067 17.8314C6.04945 18.0342 5.60037 17.9139 5.39767 17.5628L3.88167 14.937C3.67903 14.586 3.79928 14.1372 4.15026 13.9346C5.63949 13.0748 5.63946 10.9253 4.15025 10.0655C3.79926 9.86282 3.67901 9.41401 3.88165 9.06303L5.39764 6.43725C5.60034 6.08617 6.04943 5.96581 6.40065 6.16858C7.88982 7.02836 9.75182 5.9539 9.75182 4.23399C9.75182 3.82862 10.0804 3.5 10.4858 3.5ZM13.5182 2L10.4858 2C9.25201 2 8.25182 3.00019 8.25182 4.23399C8.25182 4.79884 7.64013 5.15215 7.15065 4.86955C6.08213 4.25263 4.71559 4.61859 4.0986 5.68725L2.58261 8.31303C1.96575 9.38146 2.33183 10.7477 3.40025 11.3645C3.88948 11.647 3.88947 12.3531 3.40026 12.6355C2.33184 13.2524 1.96578 14.6186 2.58263 15.687L4.09863 18.3128C4.71562 19.3814 6.08215 19.7474 7.15067 19.1305C7.64015 18.8479 8.25182 19.2012 8.25182 19.766C8.25182 20.9998 9.25201 22 10.4858 22H13.5182C14.7519 22 15.7518 20.9998 15.7518 19.7663C15.7518 19.2015 16.3632 18.8487 16.852 19.1309C17.9202 19.7476 19.2862 19.3816 19.9029 18.3134L21.4193 15.6869C22.0361 14.6185 21.6701 13.2523 20.6017 12.6355C20.1125 12.3531 20.1125 11.647 20.6017 11.3645C21.6701 10.7477 22.0362 9.38152 21.4193 8.3131L19.903 5.68667C19.2862 4.61842 17.9202 4.25241 16.852 4.86917C16.3632 5.15138 15.7518 4.79856 15.7518 4.23377C15.7518 3.00024 14.7519 2 13.5182 2ZM9.6659 11.9999C9.6659 10.7103 10.7113 9.66493 12.0009 9.66493C13.2905 9.66493 14.3359 10.7103 14.3359 11.9999C14.3359 13.2895 13.2905 14.3349 12.0009 14.3349C10.7113 14.3349 9.6659 13.2895 9.6659 11.9999ZM12.0009 8.16493C9.88289 8.16493 8.1659 9.88191 8.1659 11.9999C8.1659 14.1179 9.88289 15.8349 12.0009 15.8349C14.1189 15.8349 15.8359 14.1179 15.8359 11.9999C15.8359 9.88191 14.1189 8.16493 12.0009 8.16493Z"
          fill=""
        />
      </svg>
    ),
    path: '/trading',
    requiredPermissions: [
      PermissionEnum.TRADING_MARKETS,
      PermissionEnum.TRADING_PAIRS,
      PermissionEnum.TRADING_FEE_LEVELS,
    ],
    subItems: [
      {
        name: 'Markets',
        path: '/trading/markets',
        requiredPermissions: [PermissionEnum.TRADING_MARKETS],
        pro: false,
      },
      {
        name: 'Trading Pairs',
        path: '/trading/pairs',
        requiredPermissions: [PermissionEnum.TRADING_PAIRS],
        pro: false,
      },
      {
        name: 'Trading Fee Levels',
        path: '/trading/fee-levels',
        requiredPermissions: [PermissionEnum.TRADING_FEE_LEVELS],
        pro: false,
      },
    ],
  },
  {
    name: 'Token Management',
    icon: <BoxCubeIcon />,
    path: '/tokens',
    requiredPermissions: [PermissionEnum.TOKENS],
  },
  {
    name: 'Admin Management',
    icon: <GroupIcon />,
    requiredPermissions: [PermissionEnum.ADMINS, PermissionEnum.ROLES],
    subItems: [
      {
        name: 'Admins',
        path: '/admins',
        requiredPermissions: [PermissionEnum.ADMINS],
        pro: false,
      },
      {
        name: 'Roles',
        path: '/roles',
        requiredPermissions: [PermissionEnum.ROLES],
        pro: false,
      },
    ],
  },
];

/**
 * Utility functions to derive other configurations from the central navigation config
 */

/**
 * Generate route permissions mapping from navigation config
 * Used by routePermissions.ts
 */
export const generateRoutePermissions = (): Record<string, PermissionEnum[]> => {
  const routePermissions: Record<string, PermissionEnum[]> = {};

  // Add special routes that don't require permissions
  routePermissions['/profile'] = [];
  routePermissions['/no-access'] = [];

  // Process navigation items
  const processItem = (item: NavigationItem) => {
    // Add main item path if it exists
    if (item.path && item.requiredPermissions) {
      routePermissions[item.path] = item.requiredPermissions;
    }

    // Add sub-item paths
    if (item.subItems) {
      item.subItems.forEach((subItem) => {
        if (subItem.requiredPermissions) {
          routePermissions[subItem.path] = subItem.requiredPermissions;
        }
      });
    }
  };

  navigationConfig.forEach(processItem);

  // Add dynamic routes
  routePermissions['/users/[userId]'] = [PermissionEnum.USERS];
  routePermissions['/roles/create'] = [PermissionEnum.ROLES];
  routePermissions['/roles/[id]'] = [PermissionEnum.ROLES];

  return routePermissions;
};

/**
 * Generate flat list of accessible pages for redirect logic
 * Used by PermissionRedirectProvider
 */
export const generateRedirectOrder = (): Array<{ path: string; permissions: PermissionEnum[] }> => {
  const redirectOrder: Array<{ path: string; permissions: PermissionEnum[] }> = [];

  const processItem = (item: NavigationItem) => {
    // Add main item if it has a direct path
    if (item.path && item.requiredPermissions) {
      redirectOrder.push({
        path: item.path,
        permissions: item.requiredPermissions,
      });
    }

    // Add sub-items (these are the actual navigable pages)
    if (item.subItems) {
      item.subItems.forEach((subItem) => {
        if (subItem.requiredPermissions) {
          redirectOrder.push({
            path: subItem.path,
            permissions: subItem.requiredPermissions,
          });
        }
      });
    }
  };

  navigationConfig.forEach(processItem);
  return redirectOrder;
};

/**
 * Get required permissions for a given route
 * @param pathname - The current route pathname
 * @returns Array of required permissions or undefined if route not found
 */
export const getRoutePermissions = (pathname: string): PermissionEnum[] | undefined => {
  const routePermissions = generateRoutePermissions();

  // First try exact match
  if (routePermissions[pathname]) {
    return routePermissions[pathname];
  }

  // Try to match dynamic routes
  for (const route in routePermissions) {
    if (route.includes('[') && route.includes(']')) {
      // Convert dynamic route pattern to regex
      const regexPattern = route
        .replace(/\[([^\]]+)\]/g, '([^/]+)') // Replace [param] with capture group
        .replace(/\//g, '\\/'); // Escape forward slashes

      const regex = new RegExp(`^${regexPattern}$`);
      if (regex.test(pathname)) {
        return routePermissions[route];
      }
    }
  }

  return undefined;
};
