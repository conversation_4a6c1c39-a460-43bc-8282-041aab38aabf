import { PermissionEnum } from '@/types/enums';

export const routePermissions: Record<string, PermissionEnum[]> = {
  // Dashboard
  '/': [PermissionEnum.DASHBOARD],

  // User Management
  '/users': [PermissionEnum.USERS],
  '/users/[userId]': [PermissionEnum.USERS],

  // KYC Verification
  '/kyc': [PermissionEnum.KYC],

  // Wallet Management
  '/wallets/cold': [PermissionEnum.WALLETS_COLD],
  '/wallets/hot': [PermissionEnum.WALLETS_HOT],
  '/wallets/deposit-wallets': [PermissionEnum.WALLETS_DEPOSIT],
  '/wallets/transactions': [PermissionEnum.WALLETS_TRANSACTIONS],

  // Transaction Monitoring
  '/exchange/transactions': [PermissionEnum.TRANSACTIONS],
  '/exchange/payments': [PermissionEnum.TRANSACTIONS_PAYMENTS],

  // Trading Monitoring
  '/trading/open-orders': [PermissionEnum.TRADING_OPEN_ORDERS],
  '/trading/order-histories': [PermissionEnum.TRADING_ORDER_HISTORY],
  '/trading/trades': [PermissionEnum.TRADING_TRADES],
  '/trading/daily-volumes': [PermissionEnum.TRADING_DAILY_VOLUMES],
  '/trading/user-levels': [PermissionEnum.TRADING_USER_LEVELS],

  // Trading Management
  '/trading/markets': [PermissionEnum.TRADING_MARKETS],
  '/trading/pairs': [PermissionEnum.TRADING_PAIRS],
  '/trading/fee-levels': [PermissionEnum.TRADING_FEE_LEVELS],

  // Token Management
  '/tokens': [PermissionEnum.TOKENS],

  // Admin Management
  '/admins': [PermissionEnum.ADMINS],
  '/roles': [PermissionEnum.ROLES],
  '/roles/create': [PermissionEnum.ROLES],
  '/roles/[id]': [PermissionEnum.ROLES],

  // Profile page - accessible to all authenticated users
  '/profile': [],

  // No access page - accessible to all authenticated users
  '/no-access': [],
};

export const getRoutePermissions = (pathname: string): PermissionEnum[] | undefined => {
  // First try exact match
  if (routePermissions[pathname]) {
    return routePermissions[pathname];
  }

  // Try to match dynamic routes
  for (const route in routePermissions) {
    if (route.includes('[') && route.includes(']')) {
      // Convert dynamic route pattern to regex
      const regexPattern = route
        .replace(/\[([^\]]+)\]/g, '([^/]+)') // Replace [param] with capture group
        .replace(/\//g, '\\/'); // Escape forward slashes

      const regex = new RegExp(`^${regexPattern}$`);
      if (regex.test(pathname)) {
        return routePermissions[route];
      }
    }
  }

  return undefined;
};
