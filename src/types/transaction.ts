export type TDepositTransaction = {
  address: string;
  amount: string;
  asset: string;
  createdAt: number;
  fee: string;
  id: number;
  isExternal: true;
  memoTag: string;
  networkId: number;
  status: string;
  txHash: string;
  updatedAt: number;
  userId: number;
};

export type TWithdrawTransaction = {
  address: string;
  amount: string;
  asset: string;
  createdAt: number;
  fee: string;
  id: number;
  isExternal: true;
  memoTag: string;
  networkId: number;
  status: string;
  txHash: string;
  updatedAt: number;
  userId: number;
  requestedAt: number;
  sentAt: number;
  toUserId?: number;
};

export type TTransactionHistory = {
  amount: string;
  asset: string;
  blockNumber: number;
  confirmedAt: number;
  createdAt: number;
  fee: string;
  fromAddress: string;
  id: number;
  networkId: number;
  requiredConfirmation: number;
  timestamp: number;
  toAddress: string;
  txHash: string;
  type: string;
  status: string;
  updatedAt: number;
  transactionType: string;
  fromUserId: number;
  toUserId: number;
  userId: number;
  fromUser: any;
  toUser: any;
};
