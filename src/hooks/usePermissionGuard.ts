import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { canAccessPage } from '@/utils/permissions';
import { getRoutePermissions } from '@/config/routePermissions';
import { PermissionEnum, RoleEnum } from '@/types/enums';

/**
 * Get all accessible navigation items (including sub-items) for redirect logic
 * This creates a flat list of all navigable pages with their permissions
 */
const getNavigationItems = () => {
  return [
    // Main navigation items with direct paths
    {
      name: 'Dashboard',
      path: '/',
      requiredPermissions: [PermissionEnum.DASHBOARD],
    },
    {
      name: 'User Management',
      path: '/users',
      requiredPermissions: [PermissionEnum.USERS],
    },
    {
      name: 'KYC Verification',
      path: '/kyc',
      requiredPermissions: [PermissionEnum.KYC],
    },

    // Wallet Management sub-items
    {
      name: 'Cold Wallets',
      path: '/wallets/cold',
      requiredPermissions: [PermissionEnum.WALLETS_COLD],
    },
    {
      name: 'Hot Wallets',
      path: '/wallets/hot',
      requiredPermissions: [PermissionEnum.WALLETS_HOT],
    },
    {
      name: 'Deposit Wallets',
      path: '/wallets/deposit-wallets',
      requiredPermissions: [PermissionEnum.WALLETS_DEPOSIT],
    },
    {
      name: 'Transaction History',
      path: '/wallets/transactions',
      requiredPermissions: [PermissionEnum.WALLETS_TRANSACTIONS],
    },

    // Transaction Monitoring sub-items
    {
      name: 'Transactions History',
      path: '/exchange/transactions',
      requiredPermissions: [PermissionEnum.TRANSACTIONS],
    },
    {
      name: 'Payment History',
      path: '/exchange/payments',
      requiredPermissions: [PermissionEnum.TRANSACTIONS_PAYMENTS],
    },

    // Trading Monitoring sub-items
    {
      name: 'Open Orders',
      path: '/trading/open-orders',
      requiredPermissions: [PermissionEnum.TRADING_OPEN_ORDERS],
    },
    {
      name: 'Order History',
      path: '/trading/order-histories',
      requiredPermissions: [PermissionEnum.TRADING_ORDER_HISTORY],
    },
    {
      name: 'Trades',
      path: '/trading/trades',
      requiredPermissions: [PermissionEnum.TRADING_TRADES],
    },
    {
      name: 'Daily Volumes',
      path: '/trading/daily-volumes',
      requiredPermissions: [PermissionEnum.TRADING_DAILY_VOLUMES],
    },
    {
      name: 'User Levels',
      path: '/trading/user-levels',
      requiredPermissions: [PermissionEnum.TRADING_USER_LEVELS],
    },

    // Trading Settings sub-items
    {
      name: 'Markets',
      path: '/trading/markets',
      requiredPermissions: [PermissionEnum.TRADING_MARKETS],
    },
    {
      name: 'Trading Pairs',
      path: '/trading/pairs',
      requiredPermissions: [PermissionEnum.TRADING_PAIRS],
    },
    {
      name: 'Trading Fee Levels',
      path: '/trading/fee-levels',
      requiredPermissions: [PermissionEnum.TRADING_FEE_LEVELS],
    },

    // Token Management
    {
      name: 'Token Management',
      path: '/tokens',
      requiredPermissions: [PermissionEnum.TOKENS],
    },

    // Admin Management sub-items
    {
      name: 'Admins',
      path: '/admins',
      requiredPermissions: [PermissionEnum.ADMINS],
    },
    {
      name: 'Roles',
      path: '/roles',
      requiredPermissions: [PermissionEnum.ROLES],
    },
  ];
};

export interface UsePermissionGuardResult {
  isLoading: boolean;
  hasAccess: boolean;
  isRedirecting: boolean;
}

/**
 * Hook to guard pages based on user permissions
 * Automatically redirects users to accessible pages if they lack permissions
 */
export const usePermissionGuard = (): UsePermissionGuardResult => {
  const router = useRouter();
  const pathname = usePathname();
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const isUserLoading = useSelector((state: RootState) => state.user.loading);

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [hasAccess, setHasAccess] = useState(true);

  // Add timeout to prevent infinite redirecting state
  useEffect(() => {
    if (isRedirecting) {
      const timeout = setTimeout(() => {
        console.warn('Redirect timeout - resetting redirecting state');
        setIsRedirecting(false);
      }, 5000); // 5 second timeout

      return () => clearTimeout(timeout);
    }
  }, [isRedirecting]);

  useEffect(() => {
    console.log('🔒 Permission Guard Check:', {
      pathname,
      isUserLoading,
      hasUser: !!userInfo?.admin_id,
      isRedirecting,
    });

    // Reset redirecting state when pathname changes (redirect completed)
    if (isRedirecting) {
      console.log('✅ Redirect completed, resetting redirecting state');
      setIsRedirecting(false);
    }

    // Don't check permissions while user data is loading
    if (isUserLoading || !userInfo || !userInfo.admin_id) {
      setHasAccess(true);
      return;
    }

    // Get required permissions for current route
    const requiredPermissions = getRoutePermissions(pathname);

    // If no permissions are configured for this route, allow access
    if (!requiredPermissions) {
      console.log('✅ No permissions required for route:', pathname);
      setHasAccess(true);
      return;
    }

    // Check if user can access current page
    const canAccess = canAccessPage(userInfo, requiredPermissions);

    if (canAccess) {
      console.log('✅ User has access to route:', pathname);
      setHasAccess(true);
      return;
    }

    // User doesn't have access, find first accessible page
    console.log('❌ User lacks access to route:', pathname, 'Required:', requiredPermissions);
    setHasAccess(false);

    // Super admin can access everything, so this shouldn't happen
    if (userInfo.roles && userInfo.roles.includes(RoleEnum.SUPER_ADMIN)) {
      console.log('⚠️ Super admin without access - this should not happen');
      setHasAccess(true);
      return;
    }

    // Only start redirecting if we're not already redirecting
    if (!isRedirecting) {
      console.log('🔄 Starting redirect process...');
      setIsRedirecting(true);

      // Find first accessible navigation item
      const navItems = getNavigationItems();
      const firstAccessiblePage = navItems.find((item) =>
        canAccessPage(userInfo, item.requiredPermissions)
      );

      // Use setTimeout to ensure the redirect happens after state update
      setTimeout(() => {
        if (firstAccessiblePage) {
          console.log(`🎯 Redirecting user to accessible page: ${firstAccessiblePage.path}`);
          router.replace(firstAccessiblePage.path);
        } else {
          console.log('🚫 No accessible pages found, redirecting to no-access page');
          router.replace('/no-access');
        }
      }, 100); // Small delay to ensure state is updated
    }
  }, [pathname, userInfo, isUserLoading, router, isRedirecting]);

  return {
    isLoading: isUserLoading,
    hasAccess,
    isRedirecting,
  };
};
