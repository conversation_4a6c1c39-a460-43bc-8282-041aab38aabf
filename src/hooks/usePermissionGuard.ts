import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { canAccessPage } from '@/utils/permissions';
import { getRoutePermissions } from '@/config/routePermissions';
import { PermissionEnum, RoleEnum } from '@/types/enums';

/**
 * Get all accessible navigation items (including sub-items) for redirect logic
 * This creates a flat list of all navigable pages with their permissions
 */
const getNavigationItems = () => {
  return [
    // Main navigation items with direct paths
    {
      name: 'Dashboard',
      path: '/',
      requiredPermissions: [PermissionEnum.DASHBOARD],
    },
    {
      name: 'User Management',
      path: '/users',
      requiredPermissions: [PermissionEnum.USERS],
    },
    {
      name: 'KYC Verification',
      path: '/kyc',
      requiredPermissions: [PermissionEnum.KYC],
    },

    // Wallet Management sub-items
    {
      name: 'Cold Wallets',
      path: '/wallets/cold',
      requiredPermissions: [PermissionEnum.WALLETS_COLD],
    },
    {
      name: 'Hot Wallets',
      path: '/wallets/hot',
      requiredPermissions: [PermissionEnum.WALLETS_HOT],
    },
    {
      name: 'Deposit Wallets',
      path: '/wallets/deposit-wallets',
      requiredPermissions: [PermissionEnum.WALLETS_DEPOSIT],
    },
    {
      name: 'Transaction History',
      path: '/wallets/transactions',
      requiredPermissions: [PermissionEnum.WALLETS_TRANSACTIONS],
    },

    // Transaction Monitoring sub-items
    {
      name: 'Transactions History',
      path: '/exchange/transactions',
      requiredPermissions: [PermissionEnum.TRANSACTIONS],
    },
    {
      name: 'Payment History',
      path: '/exchange/payments',
      requiredPermissions: [PermissionEnum.TRANSACTIONS_PAYMENTS],
    },

    // Trading Monitoring sub-items
    {
      name: 'Open Orders',
      path: '/trading/open-orders',
      requiredPermissions: [PermissionEnum.TRADING_OPEN_ORDERS],
    },
    {
      name: 'Order History',
      path: '/trading/order-histories',
      requiredPermissions: [PermissionEnum.TRADING_ORDER_HISTORY],
    },
    {
      name: 'Trades',
      path: '/trading/trades',
      requiredPermissions: [PermissionEnum.TRADING_TRADES],
    },
    {
      name: 'Daily Volumes',
      path: '/trading/daily-volumes',
      requiredPermissions: [PermissionEnum.TRADING_DAILY_VOLUMES],
    },
    {
      name: 'User Levels',
      path: '/trading/user-levels',
      requiredPermissions: [PermissionEnum.TRADING_USER_LEVELS],
    },

    // Trading Settings sub-items
    {
      name: 'Markets',
      path: '/trading/markets',
      requiredPermissions: [PermissionEnum.TRADING_MARKETS],
    },
    {
      name: 'Trading Pairs',
      path: '/trading/pairs',
      requiredPermissions: [PermissionEnum.TRADING_PAIRS],
    },
    {
      name: 'Trading Fee Levels',
      path: '/trading/fee-levels',
      requiredPermissions: [PermissionEnum.TRADING_FEE_LEVELS],
    },

    // Token Management
    {
      name: 'Token Management',
      path: '/tokens',
      requiredPermissions: [PermissionEnum.TOKENS],
    },

    // Admin Management sub-items
    {
      name: 'Admins',
      path: '/admins',
      requiredPermissions: [PermissionEnum.ADMINS],
    },
    {
      name: 'Roles',
      path: '/roles',
      requiredPermissions: [PermissionEnum.ROLES],
    },
  ];
};

export interface UsePermissionGuardResult {
  isLoading: boolean;
  hasAccess: boolean;
  isRedirecting: boolean;
}

/**
 * Hook to guard pages based on user permissions
 * Automatically redirects users to accessible pages if they lack permissions
 */
export const usePermissionGuard = (): UsePermissionGuardResult => {
  const router = useRouter();
  const pathname = usePathname();
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const isUserLoading = useSelector((state: RootState) => state.user.loading);

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [hasAccess, setHasAccess] = useState(true);

  useEffect(() => {
    // Don't check permissions while user data is loading
    if (isUserLoading || !userInfo || !userInfo.admin_id) {
      return;
    }

    // Get required permissions for current route
    const requiredPermissions = getRoutePermissions(pathname);

    // If no permissions are configured for this route, allow access
    if (!requiredPermissions) {
      setHasAccess(true);
      return;
    }

    // Check if user can access current page
    const canAccess = canAccessPage(userInfo, requiredPermissions);

    if (canAccess) {
      setHasAccess(true);
      return;
    }

    // User doesn't have access, find first accessible page
    setHasAccess(false);
    setIsRedirecting(true);

    // Super admin can access everything, so this shouldn't happen
    if (userInfo.roles && userInfo.roles.includes(RoleEnum.SUPER_ADMIN)) {
      setHasAccess(true);
      setIsRedirecting(false);
      return;
    }

    // Find first accessible navigation item
    const navItems = getNavigationItems();
    const firstAccessiblePage = navItems.find((item) =>
      canAccessPage(userInfo, item.requiredPermissions)
    );

    if (firstAccessiblePage) {
      console.log(`Redirecting user to accessible page: ${firstAccessiblePage.path}`);
      router.replace(firstAccessiblePage.path);
    } else {
      // No accessible pages found, redirect to no-access page
      console.log('No accessible pages found, redirecting to no-access page');
      router.replace('/no-access');
    }
  }, [pathname, userInfo, isUserLoading, router]);

  return {
    isLoading: isUserLoading,
    hasAccess,
    isRedirecting,
  };
};
