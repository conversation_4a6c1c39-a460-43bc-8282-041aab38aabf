import { TUser } from '@/store/user.store';
import { PermissionEnum, RoleEnum } from '@/types/enums';
import { canAccessPage } from '@/utils/permissions';
import { getRoutePermissions, getAccessibleRoutes } from '@/config/routePermissions';

/**
 * Test utility to verify permission system functionality
 * This can be used in the browser console for manual testing
 */

// Mock user data for testing
export const mockUsers = {
  superAdmin: {
    admin_id: 1,
    name: 'Super Admin',
    email: '<EMAIL>',
    status: 'active',
    roles: [RoleEnum.SUPER_ADMIN],
    permissions: [
      { action: ['read', 'write'], resource: PermissionEnum.DASHBOARD },
      { action: ['read', 'write'], resource: PermissionEnum.USERS },
      { action: ['read', 'write'], resource: PermissionEnum.ADMINS },
      { action: ['read', 'write'], resource: PermissionEnum.ROLES },
    ],
  } as TUser,

  dashboardOnlyUser: {
    admin_id: 2,
    name: 'Dashboard User',
    email: '<EMAIL>',
    status: 'active',
    roles: [RoleEnum.ADMIN],
    permissions: [{ action: ['read'], resource: PermissionEnum.DASHBOARD }],
  } as TUser,

  usersOnlyUser: {
    admin_id: 3,
    name: 'Users Manager',
    email: '<EMAIL>',
    status: 'active',
    roles: [RoleEnum.ADMIN],
    permissions: [{ action: ['read', 'write'], resource: PermissionEnum.USERS }],
  } as TUser,

  noPermissionsUser: {
    admin_id: 4,
    name: 'No Permissions',
    email: '<EMAIL>',
    status: 'active',
    roles: [RoleEnum.ADMIN],
    permissions: [],
  } as TUser,

  tradingUser: {
    admin_id: 5,
    name: 'Trading Manager',
    email: '<EMAIL>',
    status: 'active',
    roles: [RoleEnum.ADMIN],
    permissions: [
      { action: ['read', 'write'], resource: PermissionEnum.TRADING_MARKETS },
      { action: ['read', 'write'], resource: PermissionEnum.TRADING_PAIRS },
      { action: ['read'], resource: PermissionEnum.TRADING_OPEN_ORDERS },
    ],
  } as TUser,

  tradingOnlyUser: {
    admin_id: 6,
    name: 'Trading Only User',
    email: '<EMAIL>',
    status: 'active',
    roles: [RoleEnum.ADMIN],
    permissions: [
      { action: ['read'], resource: PermissionEnum.TRADING_OPEN_ORDERS },
      { action: ['read'], resource: PermissionEnum.TRADING_TRADES },
    ],
  } as TUser,
};

/**
 * Test permission checking for different routes
 */
export const testPermissions = () => {
  console.log('🧪 Testing Permission System');
  console.log('============================');

  const testRoutes = [
    '/',
    '/users',
    '/kyc',
    '/admins',
    '/roles',
    '/trading/markets',
    '/trading/open-orders',
    '/wallets/cold',
    '/profile',
    '/no-access',
  ];

  Object.entries(mockUsers).forEach(([userType, user]) => {
    console.log(`\n👤 Testing ${userType} (${user.email}):`);
    console.log(`   Roles: ${user.roles.join(', ')}`);
    console.log(`   Permissions: ${user.permissions.map((p) => p.resource).join(', ')}`);

    testRoutes.forEach((route) => {
      const requiredPermissions = getRoutePermissions(route);
      const hasAccess = canAccessPage(user, requiredPermissions);
      const status = hasAccess ? '✅' : '❌';
      console.log(`   ${status} ${route} (requires: ${requiredPermissions?.join(', ') || 'none'})`);
    });

    // Test accessible routes
    const userPermissions = user.permissions.map((p) => p.resource);
    const accessibleRoutes = getAccessibleRoutes(userPermissions);
    console.log(`   📍 Accessible routes: ${accessibleRoutes.join(', ')}`);
  });
};

/**
 * Test route permission configuration
 */
export const testRouteConfig = () => {
  console.log('\n🗺️  Testing Route Configuration');
  console.log('================================');

  const testPaths = [
    '/',
    '/users',
    '/users/123',
    '/roles',
    '/roles/456',
    '/trading/markets',
    '/unknown-route',
  ];

  testPaths.forEach((path) => {
    const permissions = getRoutePermissions(path);
    console.log(`${path}: ${permissions ? permissions.join(', ') : 'No permissions configured'}`);
  });
};

/**
 * Run all tests
 */
export const runAllTests = () => {
  testPermissions();
  testRouteConfig();
  console.log('\n✨ All tests completed!');
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testPermissions = {
    runAllTests,
    testPermissions,
    testRouteConfig,
    mockUsers,
    canAccessPage,
    getRoutePermissions,
    getAccessibleRoutes,
  };
}
