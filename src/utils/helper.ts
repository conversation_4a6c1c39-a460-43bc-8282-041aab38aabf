import { toastError, toastSuccess } from '@/libs/toast';
import { EOrderSide } from '@/types/order';
import copy from 'copy-to-clipboard';
import BigNumber from 'bignumber.js';
import { EOrderType } from '../types/order';
import { isZero } from './number';

export const copyToClipboard = (message: string) => {
  try {
    copy(message);
    toastSuccess('Success', 'Copied!');
  } catch (error: any) {
    toastError('Error', error.message || 'Something went wrong!');
  }
};

export const sleep = async (ms: number) =>
  new Promise((r) => setTimeout(r, ms));

export const filterParams = (params: any) => {
  return Object.fromEntries(Object.entries(params).filter(([_, v]) => v));
};

export const getSideColor = (orderSide: EOrderSide) => {
  if (orderSide?.toUpperCase() == EOrderSide.BUY) {
    return 'var(--color-green-600)';
  }

  return 'var(--color-red-400)';
};

export const calculateFilledPercent = (filled: string | null, total: string) => {
  if (!filled || isZero(filled)) return 0;

  return BigNumber(filled).dividedBy(total).multipliedBy(100).toFixed(2);
};

export const getOrderTypeDisplay = (type: EOrderType | string) => {
  switch (type) {
    case EOrderType.LIMIT:
      return 'Limit';
    case EOrderType.MARKET:
      return 'Market';
    case EOrderType.STOP_LIMIT:
      return 'Stop Limit';
    case EOrderType.STOP_MARKET:
      return 'Stop Market';
    default:
      return type;
  }
};
