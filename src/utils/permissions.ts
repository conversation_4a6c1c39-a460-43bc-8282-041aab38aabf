import { TUser } from '@/store/user.store';
import { RoleEnum, PermissionEnum } from '@/types/enums';

export const hasPermission = (user: TUser | null, permission: PermissionEnum): boolean => {
  if (!user || !user.permissions) {
    return false;
  }

  return user.permissions.map((item) => item.resource).includes(permission);
};

export const hasAnyPermission = (
  user: TUser | null,
  requiredPermissions: PermissionEnum[]
): boolean => {
  if (!user || !user.permissions || !requiredPermissions || requiredPermissions.length === 0) {
    return false;
  }

  const userPermissions = new Set(user.permissions.map((p) => p.resource));
  return requiredPermissions.some((permission) => userPermissions.has(permission));
};

/**
 * Check if a user has all of the required permissions
 * @param user - The user object containing permissions
 * @param requiredPermissions - Array of permissions, user needs all of them
 * @returns boolean indicating if user has all required permissions
 */
export const hasAllPermissions = (
  user: TUser | null,
  requiredPermissions: PermissionEnum[]
): boolean => {
  if (!user || !user.permissions || !requiredPermissions || requiredPermissions.length === 0) {
    return false;
  }

  const userPermissions = new Set(user.permissions.map((p) => p.resource));
  return requiredPermissions.every((permission) => userPermissions.has(permission));
};

export const hasAnyRole = (user: TUser | null, requiredRoles: RoleEnum[]): boolean => {
  if (!user || !user.roles || !requiredRoles || requiredRoles.length === 0) {
    return false;
  }

  return requiredRoles.some((role) => user.roles.includes(role));
};

export const isSuperAdmin = (user: TUser | null): boolean => {
  return !!(user && user.roles && user.roles.includes(RoleEnum.SUPER_ADMIN));
};

export const canAccessNavItem = (
  user: TUser | null,
  requiredPermissions?: PermissionEnum[]
): boolean => {
  // If no permissions are required, allow access
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true;
  }

  // If user is not loaded yet, deny access
  if (!user) {
    return false;
  }

  // Super admin can access everything
  if (user.roles && user.roles.includes(RoleEnum.SUPER_ADMIN)) {
    return true;
  }

  // Check if user has any of the required permissions
  return hasAnyPermission(user, requiredPermissions);
};

/**
 * Check if a user can access a specific page/route
 * @param user - The user object containing permissions
 * @param requiredPermissions - Array of permissions required for the page
 * @returns boolean indicating if user can access the page
 */
export const canAccessPage = (
  user: TUser | null,
  requiredPermissions?: PermissionEnum[]
): boolean => {
  return canAccessNavItem(user, requiredPermissions);
};
